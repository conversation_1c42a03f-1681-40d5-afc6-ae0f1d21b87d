# Glass Manufacturing Telemetry Engine

A real-time telemetry simulation and streaming service for glass manufacturing plants. This service generates realistic telemetry data for various sensors in a glass manufacturing facility and streams it via Kafka and WebSockets.

## Features

- **Telemetry Simulation**: Generates realistic telemetry data for various sensors in a glass manufacturing plant
- **Streaming**: Streams telemetry data via Kafka and WebSockets
- **API**: Provides REST API endpoints for controlling the simulation and accessing data
- **Dashboard**: Includes a web-based dashboard for visualizing the telemetry data in real-time
- **Extensible Architecture**: Built with abstraction layers that make it easy to integrate with real sensor data

## System Architecture

```
┌─────────────────────────────────────────────────────────────────────────┐
│                           Telemetry Engine                               │
│                                                                         │
│  ┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐    │
│  │                 │     │                 │     │                 │    │
│  │    Telemetry    │────▶│    Telemetry    │────▶│    Streaming    │    │
│  │    Simulators   │     │    Generator    │     │     Service     │    │
│  │                 │     │                 │     │                 │    │
│  └─────────────────┘     └─────────────────┘     └─────────────────┘    │
│           │                       │                       │              │
│           │                       │                       │              │
└───────────┼───────────────────────┼───────────────────────┼──────────────┘
            │                       │                       │
            ▼                       ▼                       ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────────────┐
│                 │     │                 │     │                         │
│  Configuration  │     │      REST       │     │  Streaming Endpoints    │
│      Files      │     │       API       │     │  (Kafka & WebSockets)   │
│                 │     │                 │     │                         │
└─────────────────┘     └─────────────────┘     └─────────────────────────┘
                                                            │
                                                            │
                                                            ▼
                                                ┌─────────────────────────┐
                                                │                         │
                                                │   Client Applications   │
                                                │   (Dashboard, etc.)     │
                                                │                         │
                                                └─────────────────────────┘
```

## Data Flow Architecture

```
┌───────────────┐     ┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│               │     │               │     │               │     │               │
│  Simulators   │────▶│  Raw Data     │────▶│  Validation   │────▶│  Telemetry    │
│  Generate     │     │  Dictionary   │     │  & Schema     │     │  Message      │
│  Data Points  │     │               │     │  Conversion   │     │               │
│               │     │               │     │               │     │               │
└───────────────┘     └───────────────┘     └───────────────┘     └───────────────┘
                                                                          │
                                                                          │
                                                                          ▼
┌───────────────┐     ┌───────────────┐     ┌───────────────┐     ┌───────────────┐
│               │     │               │     │               │     │               │
│  Client       │◀────│  WebSocket    │◀────│  Streaming    │◀────│  Buffer       │
│  Dashboard    │     │  Server       │     │  Service      │     │  Collection   │
│               │     │               │     │               │     │               │
│               │     │               │     │               │     │               │
└───────────────┘     └───────────────┘     └───────────────┘     └───────────────┘
```

## Conceptual Flow

### 1. Application Startup
- The FastAPI app is launched via `run.py`
- The `@app.on_event("startup")` handler:
  - Starts the WebSocket & Kafka streamer
  - Starts the telemetry generator if enabled in settings

### 2. Telemetry Generator Initialization
- Loads simulators from configuration files
- Each simulator type (Temperature, Vibration, Speed, Quality, Environmental, Events) is initialized
- Special event simulators include:
  - Machine State Simulator (tracks operational states)
  - Batch Tracking Simulator (tracks production batches)
  - Quality Control Simulator (performs quality checks)

### 3. Simulation Loop
- For each active simulator:
  - Calls `generate_data()` at configured intervals
  - Returns raw data dictionaries with sensor values/states
  - Each simulator adds appropriate fields based on its type

### 4. Data Validation and Schema Conversion
- Raw data dictionaries are passed to `create_telemetry_model()`
- The function intelligently inspects data fields to determine the correct Pydantic model
- Data is validated against the appropriate schema based on sensor type and content
- For event-type sensors, specialized models are selected based on field inspection:
  - `MachineStateData`: Contains "state" and "state_durations" fields
  - `BatchTrackingData`: Contains "batch_id" field
  - `QualityControlData`: Contains "result" field
  - Generic `EventData`: Fallback for other event types

### 5. Streaming
- Validated data is collected in a buffer
- When buffer reaches configured size, a `TelemetryMessage` is created
- The message is published to:
  - Kafka topics (by sensor type)
  - WebSocket clients (by sensor type)

### 6. Client Dashboard
- Connects to WebSocket endpoints
- Parses incoming JSON messages
- Renders real-time visualizations

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/telemetry-engine.git
   cd telemetry-engine
   ```

2. Create a virtual environment and activate it:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install the dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Configuration

The service is configured using YAML files in the `config/` directory:

- `simulation.yaml`: Configuration for the telemetry simulation
  - Defines sensor types, ranges, noise levels, etc.
  - Configures event simulators (machine states, batch tracking, quality control)
- `streaming.yaml`: Configuration for the streaming services
  - Kafka settings (bootstrap servers, topics)
  - WebSocket settings (host, port, path)
- `tools.yaml`: Configuration for analysis tools

## Running the Service

1. Start the service:
   ```bash
   python run.py
   ```

   This will start the FastAPI application on http://localhost:8000.

2. Access the API documentation:
   - Swagger UI: http://localhost:8000/docs
   - ReDoc: http://localhost:8000/redoc

## Running the Client Dashboard

1. Start the client HTTP server:
   ```bash
   python client/run_client.py
   ```

   This will start a simple HTTP server on http://localhost:8080.

2. Open your browser and navigate to http://localhost:8080 to access the dashboard.

3. In the dashboard, enter the WebSocket URL (default: `ws://localhost:8765/ws/telemetry/all`) and click "Connect" to start receiving telemetry data.

## API Endpoints

### Simulation Control

- `GET /api/v1/simulation/status`: Get the current status of the simulation
- `POST /api/v1/simulation/control`: Control the simulation (start, stop, pause, reset)
- `GET /api/v1/simulation/simulators`: List all available simulators
- `GET /api/v1/simulation/simulators/{simulator_id}`: Get information about a specific simulator
- `POST /api/v1/simulation/simulators/{simulator_id}/start`: Start a specific simulator
- `POST /api/v1/simulation/simulators/{simulator_id}/stop`: Stop a specific simulator
- `POST /api/v1/simulation/simulators/{simulator_id}/reset`: Reset a specific simulator

### Streaming Control

- `GET /api/v1/simulation/streaming/status`: Get the current status of the streaming services
- `POST /api/v1/simulation/streaming/start`: Start the streaming services
- `POST /api/v1/simulation/streaming/stop`: Stop the streaming services

### WebSocket Endpoints

- `/ws/telemetry/all`: WebSocket endpoint for all telemetry data
- `/ws/telemetry/temperature`: WebSocket endpoint for temperature sensor data
- `/ws/telemetry/vibration`: WebSocket endpoint for vibration sensor data
- `/ws/telemetry/speed`: WebSocket endpoint for speed sensor data
- `/ws/telemetry/quality`: WebSocket endpoint for quality sensor data
- `/ws/telemetry/environmental`: WebSocket endpoint for environmental sensor data
- `/ws/telemetry/event`: WebSocket endpoint for event data

## Code Architecture & Abstractions

### Core Abstractions

1. **BaseSimulator (Abstract Base Class)**
   - Defines the interface for all simulators
   - Key methods: `generate_data()`, `start()`, `stop()`, `reset()`
   - Handles common functionality like timing and state management

2. **Simulator Type Hierarchy**
   - `SensorSimulator`: Base for continuous value sensors
     - `TemperatureSensor`, `VibrationSensor`, `SpeedSensor`, etc.
   - `EventSimulator`: Base for discrete event simulators
     - `MachineStateSimulator`
   - Specialized simulators:
     - `BatchTrackingSimulator`
     - `QualityControlSimulator`

3. **Data Models (Pydantic)**
   - `BaseTelemetryData`: Base model for all telemetry data
   - Specialized models for each sensor type
   - Validation and serialization handled automatically

4. **Streaming Abstraction**
   - `TelemetryStreamer`: Manages all streaming operations
   - Delegates to specialized managers:
     - `WebSocketManager`: Handles WebSocket connections
     - `KafkaManager`: Handles Kafka publishing

### Key Design Patterns

1. **Factory Pattern**
   - `create_simulator()`: Creates appropriate simulator based on type
   - `create_telemetry_model()`: Creates appropriate model based on data

2. **Observer Pattern**
   - Simulators generate data, streamer observes and distributes

3. **Strategy Pattern**
   - Different simulator implementations provide different data generation strategies

4. **Dependency Injection**
   - Configuration passed to components at initialization

## Project Structure

```
telemetry-engine/
├── client/                     # Client dashboard
│   ├── index.html              # Dashboard HTML
│   └── run_client.py           # Client HTTP server
├── config/                     # Configuration files
│   ├── simulation.yaml         # Simulation configuration
│   ├── streaming.yaml          # Streaming configuration
│   └── tools.yaml              # Tools configuration
├── src/                        # Source code
│   └── tool_simulator_service/ # Main package
│       ├── api/                # API endpoints
│       │   ├── endpoints/      # API route handlers
│       │   ├── schemas.py      # API request/response models
│       │   └── main.py         # FastAPI application
│       ├── common/             # Common utilities
│       │   ├── constants.py    # System constants
│       │   ├── exceptions.py   # Custom exceptions
│       │   └── helpers.py      # Utility functions
│       ├── config/             # Configuration loading
│       │   └── settings.py     # Settings models and loader
│       ├── telemetry/          # Telemetry simulation
│       │   ├── generator.py    # Main telemetry generator
│       │   ├── schemas.py      # Telemetry data models
│       │   ├── streamer.py     # Streaming service
│       │   └── simulators/     # Simulator implementations
│       │       ├── base.py     # Base simulator classes
│       │       └── sensor_simulator.py # Concrete simulators
│       └── tools/              # Analysis tools
├── tests/                      # Tests
├── run.py                      # Service runner
└── README.md                   # This file
```

## Integration with Real-Time Sensor Data

### Future Integration Plan

The system is designed with abstraction layers that make it easy to integrate with real sensor data sources with minimal refactoring:

1. **Create Real Data Adapters**
   - Implement a new `RealSensorAdapter` class that inherits from `BaseSimulator`
   - Override the `generate_data()` method to fetch data from real APIs instead of simulating it
   - The adapter translates real sensor data to the same format used by simulators

2. **Configuration Changes**
   - Add a new section in configuration for real data sources
   - Specify connection details, authentication, polling intervals

3. **Factory Extension**
   - Extend the `create_simulator()` factory to create real data adapters
   - Example: `if simulator_type == "real_sensor": return RealSensorAdapter(**config)`

4. **Hybrid Mode**
   - Run both simulated and real sensors simultaneously
   - Use simulated data to fill gaps in real data
   - Compare simulated vs. real data for anomaly detection

### Sample Integration Code

```python
class RealSensorAdapter(BaseSimulator):
    """Adapter for real sensor data sources."""

    def __init__(self, simulator_id: str, config: Dict[str, Any]):
        super().__init__(simulator_id, config)
        self.api_url = config.get("api_url")
        self.api_key = config.get("api_key")
        self.sensor_id = config.get("sensor_id")
        self.sensor_type = config.get("sensor_type")
        self.client = SensorApiClient(self.api_url, self.api_key)

    async def generate_data(self) -> Dict[str, Any]:
        """Fetch data from real sensor API."""
        if not self.is_running:
            raise SimulationError(f"Adapter {self.simulator_id} is not running")

        # Fetch latest reading from sensor API
        raw_data = await self.client.get_sensor_reading(self.sensor_id)

        # Transform to standard format
        data_point = {
            "timestamp": get_timestamp(),
            "simulator_id": self.simulator_id,
            "name": self.name,
            "sensor_type": self.sensor_type,
            "value": raw_data["value"],
            "unit": raw_data["unit"],
        }

        return data_point
```
Setup guide 
create env using env yamal 
run the below comand 
PYTHONPATH=./src uvicorn tool_simulator_service.api.main:app --reload
fastapi docs at 
http://localhost:8000/docs
webscokets urls 
ws://localhost:8000/ws/telemetry/all
ws://localhost:8000/ws/telemetry/kpimetrics