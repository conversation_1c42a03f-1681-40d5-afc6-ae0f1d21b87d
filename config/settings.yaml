# General Service Settings
# Example: If using FastAPI and Uvicorn
api:
  host: "0.0.0.0"
  port: 8000

  reload: true # For development
  logging:
    level: "INFO" # e.g., DEBUG, INFO, WARNING, ERROR, CRITICAL
    format: "[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s"
    date_format: "%Y-%m-%d %H:%M:%S"

  event_logging: # Settings for your dedicated event file logger
    path: "src/tool_simulator_service/agents/incident_agent/logs/events.jsonl" # Path relative to project root
    level: "INFO"

# Add other service-wide settings here
  agent:
    enabled: true
    OPENAI_API_KEY: "********************************************************************************************************************************************************************"
