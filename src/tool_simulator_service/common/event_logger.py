import logging
import json
import os
from typing import Dict, Any, Optional
from pathlib import Path # <<< ADD Path import
from datetime import datetime, timezone

# Assuming settings.py is at src/tool_simulator_service/config/settings.py
# and this file (event_logger.py) is at src/tool_simulator_service/common/event_logger.py
# then the project root (telemetry-engine/) is 3 levels up from this file's directory.
PROJECT_ROOT_DIR = Path(__file__).resolve().parents[3]

# Import settings after defining PROJECT_ROOT_DIR if settings needs it,
# but usually settings is imported directly.
from src.tool_simulator_service.config.settings import settings # Your existing import

class JsonFormatter(logging.Formatter):
    """
    Formats log records as a single JSON string per line.
    If record.msg is a dictionary, its items are merged into the top level of the JSON.
    Otherwise, record.msg is placed under a 'message' key.
    """
    def __init__(self, fmt: Optional[str] = None, datefmt: Optional[str] = None, style='%', validate=True, defaults: Optional[Dict[str, Any]] = None):
        if defaults is not None: # For Python 3.10+
             super().__init__(fmt=fmt, datefmt=datefmt, style=style, validate=validate, defaults=defaults)
        else: # For older Python or if defaults is not used
             super().__init__(fmt=fmt, datefmt=datefmt, style=style, validate=validate)


    def format(self, record: logging.LogRecord) -> str:
        log_entry: Dict[str, Any] = {
            "timestamp": self.formatTime(record, self.datefmt or '%Y-%m-%dT%H:%M:%S%z'), # Ensure default datefmt
            "level": record.levelname,
            "logger_name": record.name,
        }

        if isinstance(record.msg, dict):
            # Merge dictionary message, avoid overwriting standard fields
            for key, value in record.msg.items():
                if key not in log_entry:
                    log_entry[key] = value
                else:
                    log_entry[f"event_{key}"] = value # Prefix conflicting keys
        else:
            log_entry["message"] = record.getMessage()

        if record.exc_info:
            log_entry["exception_info"] = self.formatException(record.exc_info)
        if record.stack_info:
            log_entry["stack_info"] = self.formatStack(record.stack_info)
            
        return json.dumps(log_entry)


class EventFileLogger:
    def __init__(self, logger_instance: logging.Logger):
        self.logger = logger_instance
    
    def log_structured_event(self, event_payload: Dict[str, Any]) -> None:
        """
        Logs a structured event dictionary.
        The caller is responsible for ensuring the payload has a consistent structure,
        including fields like 'event_type' and 'event_occurrence_timestamp'.
        """
        # Add/ensure event_occurrence_timestamp if not already present by the caller.
        # This uses current time if the caller forgets.
        event_payload.setdefault('event_occurrence_timestamp', 
                                 datetime.now(timezone.utc).isoformat())
        
        self.logger.info(event_payload) 



def setup_event_file_logger( # Renamed function for clarity
    name: str = "dedicated_event_file_logger",
    relative_log_path_from_settings: Optional[str] = None,
    log_level: int = logging.INFO
) -> logging.Logger:
    path_to_resolve: str
    if relative_log_path_from_settings is None:
        default_relative_path = "logs/default_events.jsonl"
        path_to_resolve = os.getenv("APP_EVENT_LOG_PATH_RELATIVE", default_relative_path)
    else:
        path_to_resolve = relative_log_path_from_settings

    if os.path.isabs(path_to_resolve):
        final_log_file_path = Path(path_to_resolve)
    else:
        final_log_file_path = PROJECT_ROOT_DIR / path_to_resolve
    
    log_dir = final_log_file_path.parent
    os.makedirs(log_dir, exist_ok=True)

    logger_instance = logging.getLogger(name)
    logger_instance.setLevel(log_level)
    logger_instance.propagate = False # CRITICAL: Prevents logs going to console via root

    handler_exists = any(
        isinstance(h, logging.FileHandler) and Path(h.baseFilename).resolve() == final_log_file_path.resolve()
        for h in logger_instance.handlers
    )

    if not handler_exists:
        formatter = JsonFormatter(datefmt='%Y-%m-%dT%H:%M:%S%z') # ISO8601 for log_timestamp
        try:
            file_handler = logging.FileHandler(str(final_log_file_path), mode='a')
            file_handler.setFormatter(formatter)
            logger_instance.addHandler(file_handler)
        except Exception as e:
            print(f"CRITICAL ERROR setting up FileHandler for {name} at {str(final_log_file_path.resolve())}: {e}")
    return logger_instance


# --- Singleton instance ---
# Ensure `settings.api.event_logging.path` points to the correct relative path for the log file
# e.g., "src/tool_simulator_service/agents/incident_agent/logs/events.jsonl" or "logs/events.jsonl"
event_file_logger = EventFileLogger(
    setup_event_file_logger(
        relative_log_path_from_settings=settings.api.event_logging.path,
        log_level=getattr(logging, settings.api.event_logging.level.upper(), logging.INFO) # Use level from settings
    )
)