import logging
import json
import os
from typing import Dict, Any, Optional
from pathlib import Path 
from datetime import datetime, timezone

from src.tool_simulator_service.config.settings import settings

PROJECT_ROOT_DIR = Path(__file__).resolve().parents[3]

# ---------------------------
# Formatter
# ---------------------------
class JsonFormatter(logging.Formatter):
    def __init__(self, fmt: Optional[str] = None, datefmt: Optional[str] = None, style='%', validate=True, defaults: Optional[Dict[str, Any]] = None):
        if defaults is not None:  # For Python 3.10+
            super().__init__(fmt=fmt, datefmt=datefmt, style=style, validate=validate, defaults=defaults)
        else:
            super().__init__(fmt=fmt, datefmt=datefmt, style=style, validate=validate)

    def format(self, record: logging.LogRecord) -> str:
        log_entry: Dict[str, Any] = {
            "timestamp": self.formatTime(record, self.datefmt or '%Y-%m-%dT%H:%M:%S%z'),
            "level": record.levelname,
            "logger_name": record.name,
        }

        if isinstance(record.msg, dict):
            for key, value in record.msg.items():
                if key not in log_entry:
                    log_entry[key] = value
                else:
                    log_entry[f"event_{key}"] = value
        else:
            log_entry["message"] = record.getMessage()

        if record.exc_info:
            log_entry["exception_info"] = self.formatException(record.exc_info)
        if record.stack_info:
            log_entry["stack_info"] = self.formatStack(record.stack_info)
        
        return json.dumps(log_entry)

# ---------------------------
# Logger Wrapper
# ---------------------------
class EventFileLogger:
    def __init__(self, logger_instance: logging.Logger):
        self.logger = logger_instance

    def log_structured_event(self, event_payload: Dict[str, Any]) -> None:
        event_payload.setdefault('event_occurrence_timestamp', datetime.now(timezone.utc).isoformat())
        self.logger.info(event_payload)

# ---------------------------
# Setup Function
# ---------------------------
def setup_event_file_logger(
    name: str,
    path: str,
    log_level: int = logging.INFO
) -> logging.Logger:
    final_log_file_path = Path(path) if os.path.isabs(path) else PROJECT_ROOT_DIR / path
    log_dir = final_log_file_path.parent
    os.makedirs(log_dir, exist_ok=True)

    logger_instance = logging.getLogger(name)
    logger_instance.setLevel(log_level)
    logger_instance.propagate = False

    handler_exists = any(
        isinstance(h, logging.FileHandler) and Path(h.baseFilename).resolve() == final_log_file_path.resolve()
        for h in logger_instance.handlers
    )

    if not handler_exists:
        try:
            file_handler = logging.FileHandler(str(final_log_file_path), mode='a')
            file_handler.setFormatter(JsonFormatter(datefmt='%Y-%m-%dT%H:%M:%S%z'))
            logger_instance.addHandler(file_handler)
        except Exception as e:
            print(f"CRITICAL ERROR setting up FileHandler for {name} at {str(final_log_file_path.resolve())}: {e}")
    
    return logger_instance

# ---------------------------
# Singletons for use elsewhere
# ---------------------------
event_file_logger = EventFileLogger(
    setup_event_file_logger(
        name="event_logger",
        path=settings.api.event_logging.path,
        log_level=getattr(logging, settings.api.event_logging.level.upper(), logging.INFO)
    )
)

incident_file_logger = EventFileLogger(
    setup_event_file_logger(
        name="incident_logger",
        path="/home/<USER>/Downloads/telemetry-engine/src/tool_simulator_service/agents/incident_agent/logs/incident.jsonl",#Hardcoded need to move  this to config 
        log_level=logging.INFO
    )
)
