"""
Calculation tool for computing KPIs from telemetry data.
"""

from collections import defaultdict, deque
from statistics import mean
from typing import Dict, Any, List, Optional

from pydantic import BaseModel
from ..common.logging_setup import get_logger
from ..common.event_logger import event_file_logger
from ..common.helpers import get_timestamp

logger = get_logger(__name__)


class SensorStats(BaseModel):
    min: float
    max: float
    avg: float
    pct_change: float
    status: str


class SensorStatsTracker:
    def __init__(self, maxlen: int = 10000):
        self.history = defaultdict(lambda: deque(maxlen=maxlen))

    def update(self, sensor_id: str, value: float) -> SensorStats:
        history = self.history[sensor_id]
        history.append(value)

        min_val = min(history)
        max_val = max(history)
        avg_val = mean(history)
        pct_change = ((value - avg_val) / avg_val * 100) if avg_val != 0 else 0

        status = "Normal"
        if abs(pct_change) > 20:
            status = "Alert"
        elif abs(pct_change) > 10:
            status = "Warning"
        if status == "Alert":
            event_file_logger.log_structured_event({
                "type": "sensor_anomaly",
                "event_description": f"Sensor {status}: {sensor_id}",
                "simulator_id": sensor_id,
                "sensor_name": sensor_id, # Placeholder, might need actual name mapping
                "sensor_type": sensor_id.split("_")[0], # Placeholder, might need actual type mapping
                "status": status,
                "value": value,
                "pct_change": pct_change,
                "data_timestamp": get_timestamp() # Placeholder, get from data
            })
        return SensorStats(
            min=round(min_val, 2),
            max=round(max_val, 2),
            avg=round(avg_val, 2),
            pct_change=round(pct_change, 2),
            status=status
        ).dict()

# Define these as class constants or configuration
MIN_HVAC_READINGS_FOR_EFFICIENCY = 4 # e.g., at least one reading from each of the 4 zones
# Represents a typical energy sum for a stable, short period if HVAC values are ~3 each for 4 zones
# This helps avoid division by very small numbers if only a fraction of sensors reported initially.
# Adjust based on expected sum over your typical KPI calculation interval.
# If your KPI interval is 1 second, and each of 4 HVAC sensors reports ~3kW, sum is ~12.
# If they report energy directly, adjust accordingly.
MINIMUM_TOTAL_ENERGY_FOR_VALID_EFFICIENCY = 4.0 # If sum(hvac) is less than this, efficiency is suspect or 0.
DEFAULT_TOTAL_ENERGY_FALLBACK = 50.0 # Fallback if NO hvac data at all (less likely if sensors always run)
                                     # This should be a somewhat high energy cost to reflect an unknown but operating state.

# Define a realistic cap for energy efficiency if the formula tends to produce extremes.
# This is a pragmatic clamp. E.g. if 150-200 is a "very good" efficiency.
MAX_REALISTIC_ENERGY_EFFICIENCY = 500.0


class Kpimetrics(BaseModel):
    OEE: float
    Machine_Uptime: float
    Production_Rate: float
    Defect_Rate: float
    Energy_Efficiency: float
    Labor_Efficiency: float
    # Optional: Add a field to indicate data sufficiency for certain KPIs
    # energy_efficiency_data_sufficient: bool


class KpiCalculationTool:
    def __init__(self, num_hvac_zones: int = 4): # Pass number of zones for better checks
        self.buffer: List[Dict[str, Any]] = []
        self.num_hvac_zones = num_hvac_zones
        # Consider making these configurable if needed
        self.MIN_HVAC_READINGS_FOR_EFFICIENCY = num_hvac_zones
        self.MINIMUM_TOTAL_ENERGY_FOR_VALID_EFFICIENCY = float(num_hvac_zones) # e.g. 1.0 per zone as an absolute minimum sum
        self.DEFAULT_TOTAL_ENERGY_FALLBACK = float(num_hvac_zones * 10) # A higher default if no data
        self.MAX_REALISTIC_ENERGY_EFFICIENCY = 500.0


    def update(self, batch: List[Dict[str, Any]]) -> None:
        self.buffer.extend(batch)

    def reset(self) -> None:
        self.buffer.clear()

    def _extract_values(self, keyword: str, required: bool = False) -> List[float]:
        values = [
            entry["value"]
            for entry in self.buffer
            if keyword in entry.get("simulator_id", "") and isinstance(entry.get("value"), (int, float))
        ]
        if required and not values:
            logger.warning(f"No data found for required sensor type '{keyword}' in current buffer.")
            # Depending on strictness, you could raise an error or return a specific indicator
        return values

    def _safe_mean(self, values: List[float], default: float) -> float:
        if not values:
            return default
        return mean(values)

    def compute_kpis(self) -> Kpimetrics:
        # Inside KpiCalculationTool.compute_kpis()

        # ADD THIS TEMPORARY DEBUGGING FOR THE FIRST RUN
        import time # Add import at the top of the file
        if not hasattr(self, '_first_run_logged'):
            logger.info("--- FIRST KPI CALCULATION RUN ---")
            logger.info(f"Current time: {time.time()}")
            logger.info(f"Buffer size: {len(self.buffer)}")
            # Log a few sample entries from the buffer to check format and IDs
            if self.buffer:
                logger.info(f"Sample buffer entries (first 5): {self.buffer[:5]}")
                logger.info(f"Sample buffer entries (last 5): {self.buffer[-5:]}")

            hvac_values = self._extract_values("hvac") # Use your actual keyword
            temperature_values = self._extract_values("temperature")
            humidity_values = self._extract_values("humidity")
            aqi_values = self._extract_values("aqi")
            noise_values = self._extract_values("noise")
            light_values = self._extract_values("light")

            logger.info(f"Extracted # HVAC values: {len(hvac_values)}")
            # Log the actual HVAC values if not too many, or min/max/sum
            if hvac_values:
                logger.info(f"Sum of HVAC values: {sum(hvac_values)}")
                logger.info(f"Min HVAC value: {min(hvac_values)}")
                logger.info(f"Max HVAC value: {max(hvac_values)}")
            else:
                logger.info("No HVAC values extracted!")

            # Continue with calculations...
            mean_aqi = self._safe_mean(aqi_values, 50.0)
            base_production = 150
            produced_units = max(0, min(160, base_production - (mean_aqi - 40)))
            logger.info(f"Calculated produced_units: {produced_units}")

            energy_eff = 0.0
            if produced_units > 0:
                # ... [logic for insufficient/no hvac data] ...
                if hvac_values and len(hvac_values) >= self.MIN_HVAC_READINGS_FOR_EFFICIENCY:
                    total_hvac_energy = sum(hvac_values)
                    logger.info(f"Using total_hvac_energy: {total_hvac_energy}") # Log the sum used
                    if total_hvac_energy > 0:
                        effective_denominator = max(total_hvac_energy, self.MINIMUM_TOTAL_ENERGY_FOR_VALID_EFFICIENCY)
                        logger.info(f"Using effective_denominator: {effective_denominator}") # Log the denominator used!
                        calculated_eff = (produced_units / effective_denominator) * 100
                        logger.info(f"Raw calculated_eff (before cap): {calculated_eff}") # Log the raw value!
                        energy_eff = min(calculated_eff, self.MAX_REALISTIC_ENERGY_EFFICIENCY)
                    else:
                        energy_eff = 0.0
                # Add logging for the other branches (no data, insufficient data) if needed
            logger.info(f"Final energy_eff for first run: {energy_eff}") # Log the final value
            self._first_run_logged = True # Prevent logging on subsequent runs
            logger.info("--- END OF FIRST RUN LOG ---")

# ... rest of the compute_kpis method ...
        # Extract all necessary values first
        hvac_values = self._extract_values("hvac") # Example: "zone_hvac" or "hvac"
        temperature_values = self._extract_values("temperature")
        humidity_values = self._extract_values("humidity")
        aqi_values = self._extract_values("aqi")
        noise_values = self._extract_values("noise")
        light_values = self._extract_values("light")

        # Calculate means with fallbacks
        mean_temp = self._safe_mean(temperature_values, 25.0)
        mean_hum = self._safe_mean(humidity_values, 60.0)
        mean_aqi = self._safe_mean(aqi_values, 50.0) # AQI default if no data
        mean_noise = self._safe_mean(noise_values, 45.0)
        mean_light = self._safe_mean(light_values, 500.0)

        # --- Production Calculation ---
        # This assumes produced_units is for the current buffer's time window
        # If AQI data is missing, mean_aqi is 50, so produced_units = 150 - (50-40) = 140
        base_production = 150
        produced_units = max(0, min(160, base_production - (mean_aqi - 40))) # Ensure non-negative

        # --- Energy Efficiency Calculation ---
        energy_eff = 0.0  # Default to 0 (e.g., if no production or insufficient energy data)
        # energy_data_sufficient = False # Optional flag

        if produced_units > 0: # Only calculate efficiency if there's production
            if not hvac_values:
                logger.warning("No HVAC data in buffer. Using default fallback energy for efficiency calculation.")
                total_hvac_energy = self.DEFAULT_TOTAL_ENERGY_FALLBACK
                # energy_data_sufficient = False
            elif len(hvac_values) < self.MIN_HVAC_READINGS_FOR_EFFICIENCY:
                logger.warning(f"Insufficient HVAC readings ({len(hvac_values)} < {self.MIN_HVAC_READINGS_FOR_EFFICIENCY}). "
                               "Energy efficiency might be unreliable or set to 0.")
                # Option 1: Use a fallback (could still be skewed)
                # total_hvac_energy = self.DEFAULT_TOTAL_ENERGY_FALLBACK
                # Option 2: Consider efficiency 0 or use a higher minimum energy sum
                total_hvac_energy = sum(hvac_values)
                if total_hvac_energy < self.MINIMUM_TOTAL_ENERGY_FOR_VALID_EFFICIENCY * 2 : # Stricter for very few readings
                     energy_eff = 0.0 # Not enough data for meaningful calculation
                     total_hvac_energy = 0 # Prevent division below
                # energy_data_sufficient = False
            else:
                total_hvac_energy = sum(hvac_values)
                # energy_data_sufficient = True

            if total_hvac_energy > 0: # Proceed if there's some energy value
                # If sum is positive but too small, it can lead to huge efficiency.
                # Use a minimum denominator to prevent this.
                effective_denominator = max(total_hvac_energy, self.MINIMUM_TOTAL_ENERGY_FOR_VALID_EFFICIENCY)
                
                calculated_eff = (produced_units / effective_denominator) * 100
                # Cap the efficiency to a realistic maximum
                energy_eff = min(calculated_eff, self.MAX_REALISTIC_ENERGY_EFFICIENCY)
            else: # If total_hvac_energy ended up <=0 (e.g. after checks for insufficient data)
                energy_eff = 0.0
        else: # No production
            energy_eff = 0.0
            # energy_data_sufficient = True # Data might be sufficient, but no production means 0 efficiency

        # --- Other KPI Formulas (mostly unchanged, but ensure robustness) ---
        defects = (mean_hum - 50) * 0.05 if mean_hum > 50 else 0.5
        defects = max(0, defects) # Ensure non-negative

        # Uptime: ensure it's based on conditions that can be assessed from buffer
        uptime_minutes = 55 if mean_temp < 30 and mean_aqi < 65 else 45 # This is a fixed heuristic
        uptime_pct = (uptime_minutes / 60) * 100

        # Performance: ensure produced_units_for_performance is well-defined
        # If base_production is theoretical max for the period, this is fine
        performance = (produced_units / base_production) * 100 if base_production > 0 else 0

        # Quality
        # Ensure defects are scaled appropriately to produced_units
        # If produced_units is 0, quality could be 1 (no production, no defects from it) or 0.
        # Let's say 100% quality if no units to have defects on.
        quality_pct = (1 - (defects / produced_units)) * 100 if produced_units > 0 else 100.0
        quality_pct = max(0.0, min(100.0, quality_pct)) # Clamp

        if mean_noise > 60:
            noise_penalty_factor = (mean_noise - 60) / 100 # e.g. 0.1 for 70dB
            quality_pct *= max(0.0, 1.0 - noise_penalty_factor)

        # OEE
        oee = (uptime_pct/100) * (performance/100) * (quality_pct/100) * 100 # As percentage
        oee = max(0.0, min(100.0, oee))

        defect_rate = (defects / produced_units) * 100 if produced_units > 0 else 0.0
        defect_rate = max(0.0, defect_rate)

        labor_eff = 100.0 - ((mean_light - 500) * 0.02) # Assumes 500 lux is optimal
        labor_eff = min(max(labor_eff, 0.0), 100.0)

        result = Kpimetrics(
            OEE=round(oee, 2),
            Machine_Uptime=round(uptime_pct, 2),
            Production_Rate=round(produced_units, 2), # This is units per buffer window
            Defect_Rate=round(defect_rate, 2),
            Energy_Efficiency=round(energy_eff, 2),
            Labor_Efficiency=round(labor_eff, 2),
            # energy_efficiency_data_sufficient=energy_data_sufficient # Optional
        )

        logger.debug(f"📊 Computed KPIs: {result.dict(exclude_none=True)}")
        return result