"""
Updated sensor simulator for zone-based telemetry.
Only includes relevant sensors as per updated config.
"""
from typing import Any, Dict
from ...common.constants import (
    SENSOR_TYPE_TEMPERATURE,
    SENSOR_TYPE_HUMIDITY,
    SENSOR_TYPE_PRESSURE,
    SENSOR_TYPE_CO2,
    SENSOR_TYPE_AQI,
    SENSOR_TYPE_NOISE,
    SENSOR_TYPE_ILLUMINATION,
    SENSOR_TYPE_HVAC,
    SENSOR_TYPE_EVENT,
)
from ...common.exceptions import ValidationError
from ...common.logging_setup import get_logger
from ...common.helpers import add_noise, apply_drift, clamp, get_timestamp, get_unix_timestamp, weighted_choice
from .base import SensorSimulator, EventSimulator, BatchTrackingSimulator, QualityControlSimulator, BaseSimulator

logger = get_logger(__name__)


class ZoneSensor(SensorSimulator):
    """
    Generic zone-based sensor simulator used for all continuous sensors like:
    temperature, humidity, pressure, CO2, AQI, noise, illumination, HVAC.
    """
    def __init__(self, simulator_id: str, config: Dict[str, Any], sensor_type: str):
        self.sensor_type = sensor_type
        super().__init__(simulator_id, config)
        


class MachineStateSimulator(EventSimulator):
    """
    Simulator for machine state events.
    """
    def __init__(self, simulator_id: str, config: Dict[str, Any]):
        super().__init__(simulator_id, config)
        self.sensor_type = SENSOR_TYPE_EVENT
        self.state_start_time = get_unix_timestamp()
        self.state_durations = {state: 0.0 for state in self.possible_states}

    async def generate_data(self) -> Dict[str, Any]:
        event_data = await super().generate_data()
        current_time = get_unix_timestamp()
        if "state" in event_data and event_data["state"] != self.current_state:
            duration = current_time - self.state_start_time
            self.state_durations[self.current_state] = self.state_durations.get(self.current_state, 0.0) + duration
            self.state_start_time = current_time
            self.current_state = event_data["state"]
        event_data["sensor_type"] = self.sensor_type
        event_data["state_durations"] = self.state_durations.copy()
        return event_data


# Factory function to create the appropriate simulator based on type
def create_simulator(simulator_type: str, simulator_id: str, config: Dict[str, Any]) -> BaseSimulator:
    simulator_classes = {
        SENSOR_TYPE_TEMPERATURE: lambda sid, cfg: ZoneSensor(sid, cfg, SENSOR_TYPE_TEMPERATURE),
        SENSOR_TYPE_HUMIDITY: lambda sid, cfg: ZoneSensor(sid, cfg, SENSOR_TYPE_HUMIDITY),
        SENSOR_TYPE_PRESSURE: lambda sid, cfg: ZoneSensor(sid, cfg, SENSOR_TYPE_PRESSURE),
        SENSOR_TYPE_CO2: lambda sid, cfg: ZoneSensor(sid, cfg, SENSOR_TYPE_CO2),
        SENSOR_TYPE_AQI: lambda sid, cfg: ZoneSensor(sid, cfg, SENSOR_TYPE_AQI),
        SENSOR_TYPE_NOISE: lambda sid, cfg: ZoneSensor(sid, cfg, SENSOR_TYPE_NOISE),
        SENSOR_TYPE_ILLUMINATION: lambda sid, cfg: ZoneSensor(sid, cfg, SENSOR_TYPE_ILLUMINATION),
        SENSOR_TYPE_HVAC: lambda sid, cfg: ZoneSensor(sid, cfg, SENSOR_TYPE_HVAC),
        "machine_state": MachineStateSimulator,
        "batch_tracking": BatchTrackingSimulator,
        "quality_control": QualityControlSimulator,
    }

    if simulator_type not in simulator_classes:
        raise ValidationError("simulator_type", f"Unsupported simulator type: {simulator_type}")

    return simulator_classes[simulator_type](simulator_id, config)
