"""
Base simulator classes for generating telemetry data.
"""
import asyncio
import time # <<< ADDED
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
import random
from ...common.constants import SENSOR_TYPE_EVENT
from ...common.exceptions import SimulationError, ValidationError
from ...common.helpers import add_noise, apply_drift, clamp, get_timestamp, get_unix_timestamp, weighted_choice
from ...common.logging_setup import get_logger

logger = get_logger(__name__)


class BaseSimulator(ABC):
    """
    Abstract base class for all simulators.
    """
    def __init__(self, simulator_id: str, config: Dict[str, Any]):
        """
        Initialize the base simulator.

        Args:
            simulator_id: Unique identifier for this simulator
            config: Configuration dictionary for this simulator
        """
        self.simulator_id = simulator_id
        self.config = config
        self.name = config.get("name", simulator_id)
        self.last_update_time = get_unix_timestamp()
        self.is_running = False
        # --- Additions for forcing values ---
        self._forced_value: Optional[float] = None
        self._force_end_time: Optional[float] = None
        # ------------------------------------
        #self.validate_config() # Keep this if you have it uncommented
        logger.debug(f"Initialized simulator: {self.simulator_id}")

    @abstractmethod
    def validate_config(self):
        """
        Validate the simulator configuration.

        Raises:
            ValidationError: If the configuration is invalid
        """
        pass

    @abstractmethod
    async def generate_data(self) -> Dict[str, Any]:
        """
        Generate a single data point.

        Returns:
            Dict[str, Any]: The generated data point
        """
        pass

    def get_metadata(self) -> Dict[str, Any]:
        """
        Get metadata about this simulator.

        Returns:
            Dict[str, Any]: Metadata dictionary
        """
        return {
            "simulator_id": self.simulator_id,
            "name": self.name,
            "type": self.__class__.__name__,
            "config": self.config,
        }

    def start(self):
        """Start the simulator."""
        self.is_running = True
        self.last_update_time = get_unix_timestamp()
        logger.info(f"Started simulator: {self.simulator_id}")

    def stop(self):
        """Stop the simulator."""
        self.is_running = False
        # Clear any forced value when stopping
        self._clear_forced_value()
        logger.info(f"Stopped simulator: {self.simulator_id}")

    def get_elapsed_time(self) -> float:
        """
        Get the elapsed time since the last update.

        Returns:
            float: Elapsed time in seconds
        """
        current_time = get_unix_timestamp()
        elapsed = current_time - self.last_update_time
        # Don't update last_update_time here, let generate_data do it
        # self.last_update_time = current_time
        return elapsed

    # --- Methods for forcing values ---
    def set_forced_value(self, value: float, duration_seconds: float = 1.0):
        """
        Temporarily forces the simulator to output a specific value.
        Only applicable to simulators that produce numeric 'value' fields.
        """
        # Check if this simulator type should support forcing numeric values
        # (e.g., SensorSimulator does, EventSimulator might not)
        if not isinstance(self, SensorSimulator): # Example check
             logger.warning(f"Simulator {self.simulator_id} type {type(self).__name__} may not support numeric value forcing.")
             # Depending on requirements, either proceed or raise an error/return early

        # if not self.is_running: # Decide if forcing should work on stopped simulators
        #      logger.warning(f"Cannot force value on stopped simulator {self.simulator_id}")
        #      return

        self._forced_value = value
        self._force_end_time = time.time() + duration_seconds
        logger.info(f"Simulator {self.simulator_id} value FORCED to {value} for {duration_seconds}s")

    def _clear_forced_value(self):
        """Clears any forced value state."""
        if self._force_end_time is not None:
             logger.info(f"Forced value duration ended for {self.simulator_id}. Resuming normal simulation.")
        self._forced_value = None
        self._force_end_time = None

    def _get_forced_value_if_active(self) -> Optional[float]:
        """
        Checks if a forced value is active and returns it. Returns None otherwise.
        """
        if self._force_end_time is not None and self._forced_value is not None:
            now = time.time()
            if now < self._force_end_time:
                # Still active: return the forced value
                return self._forced_value
            else:
                # Duration expired: clear state
                self._clear_forced_value()
                return None # Indicate normal generation needed
        return None # No force active
    # ---------------------------------

class SensorSimulator(BaseSimulator):
    """
    Base class for continuous value sensor simulators.
    """
    def __init__(self, simulator_id: str, config: Dict[str, Any]):
        """
        Initialize the sensor simulator.

        Args:
            simulator_id: Unique identifier for this simulator
            config: Configuration dictionary for this simulator
        """
        # --- Initialize BaseSimulator first ---
        super().__init__(simulator_id, config)
        # --- Then initialize SensorSimulator specific attributes ---
        self.unit = config.get("unit", "")
        self.min_value = float(config.get("min_value", 0.0))
        self.max_value = float(config.get("max_value", 100.0))
        self.initial_value = float(config.get("initial_value", (self.min_value + self.max_value) / 2))
        self.noise_std_dev = float(config.get("noise_std_dev", 0.0))
        self.drift_per_second = float(config.get("drift_per_second", 0.0))
        self.frequency_hz = float(config.get("frequency_hz", 1.0))
        # --- current_value should start at initial_value after validation ---
        self.current_value = self.initial_value # Temporarily set, might be adjusted by validation
        # --- Run validation ---
        self.validate_config()
        # --- Set current_value definitively after validation ---
        self.current_value = self.initial_value

    def validate_config(self):
        """
        Validate the sensor simulator configuration.

        Raises:
            ValidationError: If the configuration is invalid
        """
        if self.min_value >= self.max_value:
            raise ValidationError("min_value/max_value",
                                 f"min_value ({self.min_value}) must be less than max_value ({self.max_value})")

        # Ensure initial value is checked against validated min/max
        if self.initial_value < self.min_value or self.initial_value > self.max_value:
             # Correct the initial value if out of bounds? Or raise error. Raising is safer.
             # self.initial_value = clamp(self.initial_value, self.min_value, self.max_value)
             # logger.warning(f"Initial value {self.config.get('initial_value')} was outside [{self.min_value}, {self.max_value}]. Clamped to {self.initial_value}.")
             raise ValidationError("initial_value",
                                  f"initial_value ({self.initial_value}) must be between min_value ({self.min_value}) and max_value ({self.max_value})")


        if self.noise_std_dev < 0:
            raise ValidationError("noise_std_dev",
                                 f"noise_std_dev ({self.noise_std_dev}) must be non-negative")

        if self.frequency_hz <= 0:
            raise ValidationError("frequency_hz",
                                 f"frequency_hz ({self.frequency_hz}) must be positive")

    # --- MODIFIED generate_data ---
    async def generate_data(self) -> Dict[str, Any]:
        if not self.is_running:
            # Return None or empty dict? Or raise error? Let's raise for now.
            raise SimulationError(f"Simulator {self.simulator_id} is not running")

        # Check for forced value first
        forced_value = self._get_forced_value_if_active()

        if forced_value is not None:
            # Use the forced value directly
            final_value = forced_value
            # We might skip updating self.current_value to not mess up drift baseline
            # Or update it - depends on desired behavior after force ends.
            # Let's NOT update self.current_value based on forced value.
            logger.debug(f"Simulator {self.simulator_id} using FORCED value: {final_value}")
        else:
            # Generate normally if no forced value is active
            elapsed_seconds = self.get_elapsed_time() # Get time since last *real* update

            # Apply controlled drift (based on the *last known real value*)
            drifted_value = apply_drift(self.current_value, self.drift_per_second, elapsed_seconds)

            # Apply noise
            noisy_value = add_noise(drifted_value, self.noise_std_dev) if self.noise_std_dev > 0 else drifted_value

            # Clamp to safe bounds
            final_value = clamp(noisy_value, self.min_value, self.max_value)

            # Internally update the persistent 'current_value' used for next drift calculation
            self.current_value = final_value
            logger.debug(f"Simulator {self.simulator_id} generated normal value: {final_value}")

        # --- Update last_update_time regardless of forced/normal value ---
        current_unix_time = get_unix_timestamp()
        self.last_update_time = current_unix_time

        return {
            "timestamp": get_timestamp(), # Use ISO format timestamp for output
            "simulator_id": self.simulator_id,
            "name": self.name,
            "value": final_value,
            "unit": self.unit,
            "sensor_type": getattr(self, "sensor_type", None), # Get sensor_type if ZoneSensor set it
            "zone_id": self.config.get("zone_id", "unknown"),
        }
    # --- END MODIFIED generate_data ---

    def reset(self):
        """Reset the simulator to its initial state."""
        self.current_value = self.initial_value
        self.last_update_time = get_unix_timestamp()
        # Clear any potential forced value on reset
        self._clear_forced_value()
        logger.debug(f"Reset simulator: {self.simulator_id}")

class EventSimulator(BaseSimulator):
    """
    Base class for discrete event simulators.
    """
    def __init__(self, simulator_id: str, config: Dict[str, Any]):
        """
        Initialize the event simulator.

        Args:
            simulator_id: Unique identifier for this simulator
            config: Configuration dictionary for this simulator
        """
        super().__init__(simulator_id, config)
        self.possible_states = config.get("possible_states", [])
        self.state_probabilities = config.get("state_probabilities", [])
        self.initial_state = config.get("initial_state")
        self.transition_probability_per_second = float(config.get("transition_probability_per_second", 0.01))
        self.sensor_type = SENSOR_TYPE_EVENT

        # If initial state is not specified, choose one based on probabilities
        if not self.initial_state and self.possible_states:
            if self.state_probabilities and len(self.state_probabilities) == len(self.possible_states):
                self.current_state = weighted_choice(self.possible_states, self.state_probabilities)
            else:
                self.current_state = self.possible_states[0]
        else:
            self.current_state = self.initial_state

    def validate_config(self):
        """
        Validate the event simulator configuration.

        Raises:
            ValidationError: If the configuration is invalid
        """
        if not self.possible_states:
            raise ValidationError("possible_states", "possible_states must not be empty")

        if self.state_probabilities:
            if len(self.state_probabilities) != len(self.possible_states):
                raise ValidationError("state_probabilities",
                                     f"state_probabilities length ({len(self.state_probabilities)}) must match possible_states length ({len(self.possible_states)})")

            if abs(sum(self.state_probabilities) - 1.0) > 0.0001:
                raise ValidationError("state_probabilities",
                                     f"state_probabilities must sum to 1.0, got {sum(self.state_probabilities)}")

        if self.initial_state and self.initial_state not in self.possible_states:
            raise ValidationError("initial_state",
                                 f"initial_state ({self.initial_state}) must be one of possible_states {self.possible_states}")

        if self.transition_probability_per_second < 0 or self.transition_probability_per_second > 1:
            raise ValidationError("transition_probability_per_second",
                                 f"transition_probability_per_second ({self.transition_probability_per_second}) must be between 0 and 1")

    async def generate_data(self) -> Dict[str, Any]:
        """
        Generate a single event.

        Returns:
            Dict[str, Any]: The generated event
        """
        if not self.is_running:
            raise SimulationError(f"Simulator {self.simulator_id} is not running")

        # Get elapsed time since last update
        elapsed_seconds = self.get_elapsed_time()

        # Determine if a state transition should occur
        transition_probability = self.transition_probability_per_second * elapsed_seconds
        should_transition = random.random() < transition_probability

        if should_transition and len(self.possible_states) > 1:
            # Choose a new state (different from current)
            new_states = [s for s in self.possible_states if s != self.current_state]

            if self.state_probabilities:
                # Adjust probabilities to exclude current state
                current_state_idx = self.possible_states.index(self.current_state)
                adjusted_probs = [p for i, p in enumerate(self.state_probabilities) if i != current_state_idx]
                # Normalize probabilities
                total = sum(adjusted_probs)
                adjusted_probs = [p / total for p in adjusted_probs]

                self.current_state = weighted_choice(new_states, adjusted_probs)
            else:
                # Equal probability for all states
                self.current_state = random.choice(new_states)

        # Create event data
        event_data = {
            "timestamp": get_timestamp(),
            "simulator_id": self.simulator_id,
            "name": self.name,
            "sensor_type": self.sensor_type,
            "state": self.current_state,
            "zone_id": self.config.get("zone_id", "unknown"),
        }

        return event_data

    def reset(self):
        """Reset the simulator to its initial state."""
        if self.initial_state:
            self.current_state = self.initial_state
        elif self.possible_states:
            if self.state_probabilities and len(self.state_probabilities) == len(self.possible_states):
                self.current_state = weighted_choice(self.possible_states, self.state_probabilities)
            else:
                self.current_state = self.possible_states[0]

        self.last_update_time = get_unix_timestamp()
        logger.debug(f"Reset simulator: {self.simulator_id}")


class BatchTrackingSimulator(BaseSimulator):
    """
    Simulator for tracking production batches.
    """
    def __init__(self, simulator_id: str, config: Dict[str, Any]):
        """
        Initialize the batch tracking simulator.

        Args:
            simulator_id: Unique identifier for this simulator
            config: Configuration dictionary for this simulator
        """
        super().__init__(simulator_id, config)
        self.batch_duration_seconds = int(config.get("batch_duration_seconds", 3600))  # Default: 1 hour
        self.batch_prefix = config.get("batch_prefix", "BATCH-")
        self.batch_counter_start = int(config.get("batch_counter_start", 1))
        self.sensor_type = SENSOR_TYPE_EVENT

        self.current_batch_counter = self.batch_counter_start
        self.current_batch_id = f"{self.batch_prefix}{self.current_batch_counter}"
        self.batch_start_time = get_unix_timestamp()

    def validate_config(self):
        """
        Validate the batch tracking simulator configuration.

        Raises:
            ValidationError: If the configuration is invalid
        """
        if self.batch_duration_seconds <= 0:
            raise ValidationError("batch_duration_seconds",
                                 f"batch_duration_seconds ({self.batch_duration_seconds}) must be positive")

        if self.batch_counter_start < 0:
            raise ValidationError("batch_counter_start",
                                 f"batch_counter_start ({self.batch_counter_start}) must be non-negative")

    async def generate_data(self) -> Dict[str, Any]:
        """
        Generate batch tracking data.

        Returns:
            Dict[str, Any]: The generated batch data
        """
        if not self.is_running:
            raise SimulationError(f"Simulator {self.simulator_id} is not running")

        current_time = get_unix_timestamp()
        elapsed_since_batch_start = current_time - self.batch_start_time

        # Check if it's time for a new batch
        if elapsed_since_batch_start >= self.batch_duration_seconds:
            # Start a new batch
            self.current_batch_counter += 1
            self.current_batch_id = f"{self.batch_prefix}{self.current_batch_counter}"
            self.batch_start_time = current_time
            logger.info(f"Started new batch: {self.current_batch_id}")

        # Calculate progress percentage
        progress_percent = min(100.0, (elapsed_since_batch_start / self.batch_duration_seconds) * 100.0)

        # Create batch data
        batch_data = {
            "timestamp": get_timestamp(),
            "simulator_id": self.simulator_id,
            "name": self.name,
            "sensor_type": self.sensor_type,
            "batch_id": self.current_batch_id,
            "batch_counter": self.current_batch_counter,
            "batch_start_time": self.batch_start_time,
            "elapsed_seconds": elapsed_since_batch_start,
            "progress_percent": progress_percent,
            "zone_id": self.config.get("zone_id", "unknown"),
        }

        # Update last update time
        self.last_update_time = current_time

        return batch_data

    def reset(self):
        """Reset the simulator to its initial state."""
        self.current_batch_counter = self.batch_counter_start
        self.current_batch_id = f"{self.batch_prefix}{self.current_batch_counter}"
        self.batch_start_time = get_unix_timestamp()
        self.last_update_time = self.batch_start_time
        logger.debug(f"Reset simulator: {self.simulator_id}")


class QualityControlSimulator(BaseSimulator):
    """
    Simulator for quality control checks.
    """
    def __init__(self, simulator_id: str, config: Dict[str, Any]):
        """
        Initialize the quality control simulator.

        Args:
            simulator_id: Unique identifier for this simulator
            config: Configuration dictionary for this simulator
        """
        super().__init__(simulator_id, config)
        self.possible_states = config.get("possible_states", ["PASS", "WARNING", "FAIL"])
        self.state_probabilities = config.get("state_probabilities", [0.95, 0.04, 0.01])
        self.check_interval_seconds = int(config.get("check_interval_seconds", 60))  # Default: 1 minute
        self.sensor_type = SENSOR_TYPE_EVENT

        self.last_check_time = get_unix_timestamp()
        self.last_check_result = None

    def validate_config(self):
        """
        Validate the quality control simulator configuration.

        Raises:
            ValidationError: If the configuration is invalid
        """
        if not self.possible_states:
            raise ValidationError("possible_states", "possible_states must not be empty")

        if len(self.state_probabilities) != len(self.possible_states):
            raise ValidationError("state_probabilities",
                                 f"state_probabilities length ({len(self.state_probabilities)}) must match possible_states length ({len(self.possible_states)})")

        if abs(sum(self.state_probabilities) - 1.0) > 0.0001:
            raise ValidationError("state_probabilities",
                                 f"state_probabilities must sum to 1.0, got {sum(self.state_probabilities)}")

        if self.check_interval_seconds <= 0:
            raise ValidationError("check_interval_seconds",
                                 f"check_interval_seconds ({self.check_interval_seconds}) must be positive")

    async def generate_data(self) -> Dict[str, Any]:
        """
        Generate quality control data.

        Returns:
            Dict[str, Any]: The generated quality control data
        """
        if not self.is_running:
            raise SimulationError(f"Simulator {self.simulator_id} is not running")

        current_time = get_unix_timestamp()
        elapsed_since_last_check = current_time - self.last_check_time

        # Check if it's time for a new quality check
        new_check = False
        if elapsed_since_last_check >= self.check_interval_seconds:
            # Perform a new quality check
            self.last_check_result = weighted_choice(self.possible_states, self.state_probabilities)
            self.last_check_time = current_time
            new_check = True
            logger.debug(f"Quality check result: {self.last_check_result}")

        # Create quality control data
        qc_data = {
            "timestamp": get_timestamp(),
            "simulator_id": self.simulator_id,
            "name": self.name,
            "sensor_type": self.sensor_type,
            "result": self.last_check_result,
            "last_check_time": self.last_check_time,
            "new_check": new_check,
            "zone_id": self.config.get("zone_id", "unknown"),
        }

        # Update last update time
        self.last_update_time = current_time

        return qc_data

    def reset(self):
        """Reset the simulator to its initial state."""
        self.last_check_time = get_unix_timestamp()
        self.last_check_result = None
        self.last_update_time = self.last_check_time
        logger.debug(f"Reset simulator: {self.simulator_id}")
