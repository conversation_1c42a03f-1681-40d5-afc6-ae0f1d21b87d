import asyncio
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from ..config.settings import settings
from ..tools.calculation_tool import SensorStatsTracker
from ..tools.kpi_trigger_engine import KpiTriggerEngine
from ..common.helpers import get_unix_timestamp
from ..common.logging_setup import get_logger
from ..telemetry.generator import generator
from ..telemetry.websocket_manager import WebSocketManager
from ..common.event_logger import event_file_logger

logger = get_logger(__name__)


class TelemetryStreamer:
    def __init__(self):
        self.websocket_manager = WebSocketManager()
        self.stats_tracker = SensorStatsTracker()
        self.trigger_engine = KpiTriggerEngine()
        self.interval = settings.streaming.interval_ms / 1000.0
        self.kpi_interval = 15  # KPI snapshot every 15 seconds
        self.last_kpi_sent = 0
        self.running = False
        # --- New: Buffer for accumulating data for KPI calculation ---
        self.kpi_accumulation_buffer: List[Dict[str, Any]] = []
        # ----------------------------------------------------------

        logger.info("📡 TelemetryStreamer initialized")

    async def start(self):
        if not settings.streaming.enabled or not settings.streaming.websocket.enabled:
            logger.warning("Streaming or WebSocket disabled — aborting start.")
            return
        if self.running:
            logger.warning("TelemetryStreamer is already running.")
            return

        logger.info(f"🚀 Starting telemetry stream every {self.interval}s")
        # Initialize last_kpi_sent when starting to ensure the first interval is correct
        self.last_kpi_sent = get_unix_timestamp()
        self.running = True
        asyncio.create_task(self._stream_loop())

    async def stop(self):
        self.running = False
        logger.info("🛑 Telemetry streaming stopped")

    async def _stream_loop(self):
        while self.running:
            try:
                batch = await generator.get_next_data_batch()
                enriched: List[Dict[str, Any]] = []
                timestamp = datetime.now(timezone.utc).isoformat() # Get timestamp once per batch

                # 1. Enrich batch with live stats & add timestamp if needed
                for point in batch:
                    # Ensure timestamp is in each point if needed downstream,
                    # otherwise the overall batch timestamp might suffice
                    # point['timestamp'] = timestamp
                    sim_id = point.get("simulator_id")
                    val = point.get("value")
                    if sim_id and isinstance(val, (int, float)):
                        stats = self.stats_tracker.update(sim_id, val)
                        enriched.append({**point, **stats}) # Stats added here
                    # else: enriched.append(point) # Optionally keep non-stat points

                # 2. Broadcast live telemetry data (original batch or enriched?)
                # Decide if broadcast needs the stats or just raw data.
                # Using 'enriched' here for consistency.
                payload = {
                    "plant_id": settings.streaming.plant.id,
                    "plant_name": settings.streaming.plant.name,
                    "timestamp": timestamp,
                    "data": enriched, # Sending enriched data live
                }
                await self.websocket_manager.broadcast(payload, "all")
                logger.debug(f"📤 Sent {len(enriched)} live telemetry points")

                # --- Modified: Accumulate data for KPIs ---
                # 3. Add the latest enriched batch to the accumulation buffer
                self.kpi_accumulation_buffer.extend(enriched)
                # -----------------------------------------

                now = get_unix_timestamp()
                if now - self.last_kpi_sent >= self.kpi_interval:
                    logger.info(f"📈 KPI Interval reached. Processing accumulated data (buffer size: {len(self.kpi_accumulation_buffer)})")

                    if not self.kpi_accumulation_buffer:
                        logger.warning(" KPI interval reached, but accumulation buffer is empty. Skipping KPI cycle.")
                        self.last_kpi_sent = now # Reset timer even if skipped
                        continue # Skip to next stream loop iteration

                    # --- Modified: Process accumulated data ---
                    # 4. Feed the ENTIRE accumulated data to KPI trigger engine
                    self.trigger_engine.update(self.kpi_accumulation_buffer)

                    # 5. KPI Snapshot (calculated from accumulated data)
                    # get_snapshot_message internally calls compute_kpis on the buffer filled in step 4
                    snapshot = self.trigger_engine.get_snapshot_message()
                    if snapshot and snapshot.get("metrics"): # Check if snapshot is valid
                       await self.websocket_manager.broadcast(snapshot, "kpimetrics")
                       logger.debug(f"📊 KPI snapshot broadcasted: {snapshot['metrics']}")
                    else:
                        logger.warning(" KPI snapshot generation failed or returned empty metrics.")


                    # 6. Triggered KPI events (based on comparison with previous snapshot)
                    # check_triggers compares the snapshot generated above with the last one
                    # AND RESETS the trigger_engine's internal calculator buffer.
                    events = self.trigger_engine.check_triggers()
                    for event in events:
                        await self.websocket_manager.broadcast(event, "kpimetrics")
                        logger.warning(f"⚠️ KPI Event: {event['event']} | Δ{event.get('delta_pct')}% | Val: {event.get('value')} | Prev: {event.get('previous')} | Timestamp: {event.get('timestamp')}")
                        if event_file_logger:
                            log_pay_load={
                                "type": "kpi_event",
                                "event": event['event'],
                                "metric": event['metric'],
                                "value": event['value'],
                                "previous": event['previous'],
                                "delta_pct": event['delta_pct'],
                                "event_timestamp": event['timestamp']
                            }
                            event_file_logger.log_structured_event(log_pay_load)
                    # 7. Clear the accumulation buffer for the next interval
                    self.kpi_accumulation_buffer.clear()
                    # ---------------------------------------------

                    # 8. Update timestamp for the next KPI interval
                    self.last_kpi_sent = now

            except Exception as e:
                logger.exception(f"❌ Stream error: {e}") # Use logger.exception for stack trace

            await asyncio.sleep(self.interval)

# Singleton remains the same
streamer = TelemetryStreamer()