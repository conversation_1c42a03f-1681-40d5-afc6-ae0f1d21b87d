from pathlib import Path
from typing import Any, Dict, Optional
from pydantic import BaseModel, Field, model_validator
import yaml


# Base config directory: telemetry-engine/config
CONFIG_DIR = Path(__file__).resolve().parents[3] / "config"
print(f"CONFIG_DIR: {CONFIG_DIR}")


def load_yaml(file_name: str) -> Dict[str, Any]:
    """Safely load a YAML file into a dictionary."""
    file_path = CONFIG_DIR / file_name
    with open(file_path, "r") as f:
        return yaml.safe_load(f)


# --- Pydantic Models ---

class APILoggingConfig(BaseModel):
    level: str = "INFO"
    format: str = "[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s"
    date_format: str = "%Y-%m-%d %H:%M:%S"

class EventLoggingConfig(BaseModel):
    enabled: bool = True
    path: str = "src/tool_simulator_service/agents/incident_agent/logs/events.jsonl"
    level: str = "INFO"
    
class AgentConfig(BaseModel):
    enabled: bool = True
    OPENAI_API_KEY: Optional[str] = None

class APISettings(BaseModel):
    host: str = "0.0.0.0"
    port: int = 8000
    reload: bool = False
    logging: APILoggingConfig = APILoggingConfig()
    event_logging: EventLoggingConfig = EventLoggingConfig()
    agent: AgentConfig = AgentConfig()

class KafkaConfig(BaseModel):
    enabled: bool = False
    bootstrap_servers: Optional[str] = None
    topic_prefix: Optional[str] = None
    sensor_topics: Optional[Dict[str, str]] = None
    aggregated_topic: Optional[str] = None
    kpi_topic: Optional[str] = None
    client_id: Optional[str] = None
    acks: Optional[str] = None
    retries: Optional[int] = None
    batch_size: Optional[int] = None
    linger_ms: Optional[int] = None
    buffer_memory: Optional[int] = None
    security_protocol: Optional[str] = None

    @model_validator(mode="after")
    def validate_required_if_enabled(self) -> "KafkaConfig":
        if self.enabled:
            missing_fields = []
            required = [
                "bootstrap_servers", "topic_prefix", "sensor_topics",
                "aggregated_topic", "kpi_topic", "client_id", "acks",
                "retries", "batch_size", "linger_ms", "buffer_memory", "security_protocol"
            ]
            for field in required:
                if getattr(self, field) is None:
                    missing_fields.append(field)

            if missing_fields:
                raise ValueError(f"Kafka is enabled but missing required fields: {missing_fields}")

        return self


class WebSocketConfig(BaseModel):
    enabled: bool = True
    host: str = "0.0.0.0"
    port: int = 8000
    path: str = "/ws/telemetry"
    max_connections: int = 100
    channels: Dict[str, str] = Field(default_factory=dict)

class SSEConfig(BaseModel):
    enabled: bool = False
    endpoint: Optional[str] = None
    channels: Optional[Dict[str, str]] = None

    @model_validator(mode="after")
    def validate_required_if_enabled(self) -> "SSEConfig":
        if self.enabled:
            missing = []
            if not self.endpoint:
                missing.append("endpoint")
            if not self.channels:
                missing.append("channels")
            if missing:
                raise ValueError(f"SSE is enabled but missing required fields: {missing}")
        return self

class PlantMetadata(BaseModel):
    id: str = "unknown_plant"
    name: str = "Unnamed Facility"
    location: Optional[str] = None
    timezone: str = "UTC"


class StreamingSettings(BaseModel):
    enabled: bool = True
    interval_ms: int = 1000
    buffer_size: int = 10
    output_format: str = "json"
    kafka: KafkaConfig = KafkaConfig()
    websocket: WebSocketConfig = WebSocketConfig()
    sse: SSEConfig = SSEConfig()
    plant: PlantMetadata = PlantMetadata()


class SimulationSettings(BaseModel):
    enabled: bool
    update_interval_ms: int
    zones: Dict[str, Dict[str, str]] = Field(default_factory=dict)
    temperature_sensors: Dict[str, Any] = Field(default_factory=dict)
    humidity_sensors: Dict[str, Any] = Field(default_factory=dict)
    pressure_sensors: Dict[str, Any] = Field(default_factory=dict)
    co2_sensors: Dict[str, Any] = Field(default_factory=dict)
    aqi_sensors: Dict[str, Any] = Field(default_factory=dict)
    noise_sensors: Dict[str, Any] = Field(default_factory=dict)
    illumination_sensors: Dict[str, Any] = Field(default_factory=dict)
    hvac_sensors: Dict[str, Any] = Field(default_factory=dict)


class AppConfig(BaseModel):
    api: APISettings
    streaming: StreamingSettings
    simulation: SimulationSettings


# --- Loader ---

def load_config() -> AppConfig:
    api_raw = load_yaml("settings.yaml").get("api", {})
    streaming_raw = load_yaml("streaming.yaml").get("streaming", {})
    simulation_raw = load_yaml("simulation.yaml").get("simulation", {})

    return AppConfig(
        api=APISettings(**api_raw),
        streaming=StreamingSettings(**streaming_raw),
        simulation=SimulationSettings(**simulation_raw),
    )


# Singleton export
settings = load_config()
from pathlib import Path
import yaml
from rich import print
from src.tool_simulator_service.common.logging_setup import get_logger

logger = get_logger(__name__)

class ConfigValidationError(Exception):
    pass

def validate_config(config):
    logger.info("🔍 Validating configuration...")

    # Check streaming config
    plant = getattr(config.streaming, "plant", None)
    if not plant or not plant.id or not plant.name:
        raise ValueError("❌ 'streaming.plant.id' and 'streaming.plant.name' must be set")

    # Check simulation toggles and intervals
    sim = getattr(config, "simulation", None)
    if not sim or not isinstance(sim.enabled, bool):
        raise ValueError("❌ 'simulation.enabled' must be a boolean")
    if not isinstance(sim.update_interval_ms, int) or sim.update_interval_ms <= 0:
        raise ValueError("❌ 'simulation.update_interval_ms' must be a positive integer")

    # Ensure zones are defined
    if not sim.zones or not isinstance(sim.zones, dict):
        raise ValueError("❌ 'simulation.zones' must be a dictionary of zones")

    # Validate at least one sensor group
    sensor_keys = [
        "temperature_sensors", "humidity_sensors", "pressure_sensors",
        "co2_sensors", "aqi_sensors", "noise_sensors",
        "illumination_sensors", "hvac_sensors"
    ]
    sensors_present = any(getattr(sim, key, {}) for key in sensor_keys)
    if not sensors_present:
        raise ValueError("❌ No sensor groups configured under simulation")

    logger.info("✅ Configuration looks valid")
