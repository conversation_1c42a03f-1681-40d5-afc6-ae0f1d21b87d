# standalone_tool_test.py (create this new file in the same directory as your agent.py or adjust paths)
import json
from pathlib import Path
from datetime import datetime, timezone, timedelta

# --- Copy the GetRelevantLogsArgs class and get_relevant_logs_for_event function here ---
# Make sure LOG_FILE_PATH inside the tool points to your actual log file.
# For example:
# LOG_FILE_PATH = Path("src/tool_simulator_service/agent/logs/events.jsonl") # if running from project root
# Or, if standalone_tool_test.py is in src/tool_simulator_service/agent:
# LOG_FILE_PATH = Path("logs/events.jsonl")


# ----------------------------------------------------------------------
# Tool (Copied from your agent.py, ensure LOG_FILE_PATH is correct)
# ----------------------------------------------------------------------
LOG_FILE_PATH = Path("src/tool_simulator_service/agents/incident_agent/logs/events.jsonl") # ADJUST IF NEEDED

class GetRelevantLogsArgs: # Simplified for standalone test, not using Pydantic here
    pass

# @tool(args_schema=GetRelevantLogsArgs) # Remove @tool for standalone test
def get_relevant_logs_for_event_standalone(pivot_event_json_str: str, window_seconds_before: int = 30, window_seconds_after: int = 5) -> str:
    """
    (Docstring as before)
    """
    print(f"\n--- STANDALONE TOOL TEST ---")
    print(f"Type of pivot_event_json_str received: {type(pivot_event_json_str)}")
    print(f"Value of pivot_event_json_str received (raw): {pivot_event_json_str!r}")

    relevant_log_entries = []
    pivot_event_data = None

    try:
        pivot_event_data = json.loads(pivot_event_json_str)
    except Exception as e:
        print(f"Error parsing pivot_event_json_str: {e}. String was: {pivot_event_json_str!r}")
        return "Error: Invalid JSON string provided for pivot_event_json_str."

    if not isinstance(pivot_event_data, dict):
        print(f"Error: Parsed pivot_event_data is not a dictionary. Type: {type(pivot_event_data)}")
        return "Error: Parsed pivot event data is not a dictionary."

    pivot_input_timestamp_str = pivot_event_data.get("timestamp")
    print(f"Value of 'timestamp' field from PIVOT event data: {pivot_input_timestamp_str!r}")

    if not pivot_input_timestamp_str:
        print(f"Error: 'timestamp' key not found in PIVOT event data or its value is empty/None.")
        return "Error: Pivot event JSON does not contain a 'timestamp' field or it's empty."

    try:
        pivot_dt = datetime.fromisoformat(pivot_input_timestamp_str)
        if pivot_dt.tzinfo is None:
            print(f"Warning: Pivot timestamp '{pivot_input_timestamp_str}' was naive. Assuming UTC.")
            pivot_dt = pivot_dt.replace(tzinfo=timezone.utc)
    except Exception as e:
        print(f"Error parsing pivot timestamp string '{pivot_input_timestamp_str}': {e}")
        return f"Error: Invalid format for pivot timestamp: '{pivot_input_timestamp_str}'."

    window_start_dt = pivot_dt - timedelta(seconds=window_seconds_before)
    window_end_dt = pivot_dt + timedelta(seconds=window_seconds_after)
    print(f"Calculated time window: Start={window_start_dt.isoformat()}, End={window_end_dt.isoformat()}")

    if not LOG_FILE_PATH.exists():
        print(f"Error: Log file not found at {LOG_FILE_PATH}")
        return "Error: Log file not found."

    with open(LOG_FILE_PATH, 'r') as f:
        for line_number, line in enumerate(f, 1):
            try:
                log_entry = json.loads(line)
            except json.JSONDecodeError:
                continue

            log_file_entry_ts_str = log_entry.get("timestamp") # Using top-level "timestamp"

            if not log_file_entry_ts_str:
                continue

            try:
                log_dt = datetime.fromisoformat(log_file_entry_ts_str)
                if log_dt.tzinfo is None:
                    log_dt = log_dt.replace(tzinfo=timezone.utc)
            except ValueError:
                continue

            if window_start_dt <= log_dt <= window_end_dt:
                relevant_log_entries.append(line.strip())

    if not relevant_log_entries:
        print(f"No relevant log entries found for pivot {pivot_dt.isoformat()} in window.")
        return f"No relevant log entries found for pivot {pivot_dt.isoformat()} within the time window."
    
    print(f"Found {len(relevant_log_entries)} relevant entries.")
    return "\n".join(relevant_log_entries)

if __name__ == "__main__":
    # 1. Test case that should find logs
    #    Pick a timestamp from your actual events.jsonl
    #    Example: one of the "kpi_event" entries around "2025-05-20T12:42:07+0530"
    #    Its UTC equivalent would be "2025-05-20T07:12:07..."
    #    Let's use a known "timestamp" field from your log: "2025-05-20T12:42:07+0530"
    #    And craft a pivot event that would use a related UTC time.
    
    # This is the timestamp from the kpi_event (oee_change) in your sample
    # The "timestamp" field of that log entry is "2025-05-20T12:42:07+0530"
    # Its event_occurrence_timestamp is "2025-05-20T07:12:07.280099+00:00"
    # Let's make our pivot event use this UTC time.
    pivot_ts_for_test = "2025-05-20T07:12:07.280099+00:00" # UTC

    test_pivot_event1 = {
        "type": "kpi_event",
        "event": "oee_change",
        "timestamp": pivot_ts_for_test
    }
    print("\n--- Running Standalone Test 1 (Expecting logs) ---")
    result1 = get_relevant_logs_for_event_standalone(
        pivot_event_json_str=json.dumps(test_pivot_event1),
        window_seconds_before=60, # Wider window for testing
        window_seconds_after=10
    )
    print("\nResult 1:")
    print(result1)
    assert "Error:" not in result1 # Basic check

    # 2. Test case where timestamp in pivot event is missing
    test_pivot_event2 = {
        "type": "kpi_event",
        "event": "oee_change"
        # "timestamp": missing
    }
    print("\n--- Running Standalone Test 2 (Missing timestamp in pivot) ---")
    result2 = get_relevant_logs_for_event_standalone(json.dumps(test_pivot_event2))
    print("\nResult 2:")
    print(result2)
    assert "Error: Pivot event JSON does not contain a 'timestamp' field" in result2

    # 3. Test case with malformed pivot JSON
    print("\n--- Running Standalone Test 3 (Malformed pivot JSON) ---")
    result3 = get_relevant_logs_for_event_standalone("this is not json")
    print("\nResult 3:")
    print(result3)
    assert "Error: Invalid JSON string" in result3
    
    # 4. Test case with unparseable timestamp in pivot
    test_pivot_event4 = {
        "type": "kpi_event",
        "event": "oee_change",
        "timestamp": "not-a-valid-timestamp"
    }
    print("\n--- Running Standalone Test 4 (Unparseable timestamp in pivot) ---")
    result4 = get_relevant_logs_for_event_standalone(json.dumps(test_pivot_event4))
    print("\nResult 4:")
    print(result4)
    assert "Error: Invalid format for pivot timestamp" in result4

    print("\n--- Standalone testing done ---")