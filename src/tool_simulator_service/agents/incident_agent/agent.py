import os
import json
import re
from typing import Annotated, List, Literal
from typing_extensions import TypedDict
from pathlib import Path
import uuid # Import for UUID generation

from pydantic import BaseModel, ValidationError , Field
from langchain_openai import ChatOpenAI
from langchain_core.tools import tool
from langchain_core.prompts import PromptTemplate
from langchain.agents import Agent<PERSON>xecutor, create_react_agent
from src.tool_simulator_service.config.settings import settings
from datetime import datetime, timezone, timedelta

# ----------------------------------------------------------------------
# Global Counter for Display IDs (for prototype without DB)
# WARNING: This will reset every time the script restarts.
# ----------------------------------------------------------------------
_incident_sequence_counter = 0

def generate_next_display_id() -> str:
    """Generates the next sequential display ID like INC-001."""
    global _incident_sequence_counter
    _incident_sequence_counter += 1
    # Use f-string formatting to ensure leading zeros, e.g., 1 -> 001, 12 -> 012
    return f"INC-{_incident_sequence_counter:03d}"

# ----------------------------------------------------------------------
# Schema
# ----------------------------------------------------------------------

class IncidentReportModel(BaseModel):
    # The actual unique primary key (UUID)
    id: str
    # The human-readable sequential ID
    display_id: str = Field(description="A human-readable, sequentially generated incident ID (e.g., INC-001).")
    title: str
    status: Literal["in progress", "resolved"]
    severity: Literal["low", "medium", "high"]
    assigned_team: str
    reported_at: str
    description: str
    updates: List[str]

# ----------------------------------------------------------------------
# Tool (no changes needed here)
# ----------------------------------------------------------------------

LOG_FILE_PATH = Path("src/tool_simulator_service/agents/incident_agent/logs/events.jsonl")


class GetRelevantLogsArgs(BaseModel):
    pivot_event_json_str: str = Field(description="The JSON string of the pivot event that triggered the analysis. Must contain a 'timestamp' field.")
    window_seconds_before: int = Field(default=30, description="How many seconds before the pivot event's timestamp to include logs.")
    window_seconds_after: int = Field(default=5, description="How many seconds after the pivot event's timestamp to include logs (to catch immediate consequences).")

@tool(args_schema=GetRelevantLogsArgs)
def get_relevant_logs_for_event(
    tool_arg_input: str,
    window_seconds_before: int = 30,
    window_seconds_after: int = 5
) -> str:
    """
    Loads telemetry alert logs from events.jsonl that are relevant to a given pivot event's timestamp.
    It fetches logs within a time window around the pivot event.
    The input (tool_arg_input) is expected to either be the direct pivot_event_json_str,
    or a JSON string representing the entire tool arguments dictionary.
    The tool uses the top-level 'timestamp' field from log entries for window comparison.
    """
    print(f"\n--- TOOL: get_relevant_logs_for_event ---")
    print(f"[DEBUG] Initial raw tool_arg_input (type: {type(tool_arg_input)}): {tool_arg_input!r}")
    print(f"[DEBUG] Initial window_seconds_before: {window_seconds_before}")
    print(f"[DEBUG] Initial window_seconds_after: {window_seconds_after}")

    actual_pivot_event_json_str = None
    
    try:
        potential_tool_args_dict = json.loads(tool_arg_input)
        if isinstance(potential_tool_args_dict, dict) and "pivot_event_json_str" in potential_tool_args_dict:
            print(f"[INFO] tool_arg_input appears to be the full arguments dictionary. Extracting 'pivot_event_json_str'.")
            actual_pivot_event_json_str = potential_tool_args_dict.get("pivot_event_json_str")
        else:
            print(f"[INFO] tool_arg_input parsed to JSON but not the args dict. Assuming it's the pivot_event_json_str itself.")
            actual_pivot_event_json_str = tool_arg_input
    except json.JSONDecodeError:
        print(f"[INFO] tool_arg_input was not valid JSON. Assuming it IS the pivot_event_json_str itself (this might be problematic).")
        actual_pivot_event_json_str = tool_arg_input
    
    if not isinstance(actual_pivot_event_json_str, str):
        error_msg = f"[ERROR] Could not resolve actual_pivot_event_json_str to a string. Type: {type(actual_pivot_event_json_str)}, Value: {actual_pivot_event_json_str!r}"
        print(error_msg)
        return error_msg

    print(f"[DEBUG] Effective pivot_event_json_str to be parsed: {actual_pivot_event_json_str!r}")
    print(f"[DEBUG] Effective window_seconds_before: {window_seconds_before}")
    print(f"[DEBUG] Effective window_seconds_after: {window_seconds_after}")

    relevant_log_entries = []
    pivot_event_data = None

    try:
        pivot_event_data = json.loads(actual_pivot_event_json_str)
    except json.JSONDecodeError as e:
        print(f"[ERROR] Parsing the effective pivot_event_json_str failed: {e}. String was: {actual_pivot_event_json_str!r}")
        return "Error: Invalid JSON string provided for the pivot event."
    except TypeError as e:
        print(f"[ERROR] TypeError while parsing effective pivot_event_json_str (was it None or not a string?): {e}. Value: {actual_pivot_event_json_str!r}")
        return "Error: Pivot event data was not a valid string for JSON parsing."


    if not isinstance(pivot_event_data, dict):
        print(f"[ERROR] Parsed pivot_event_data is not a dictionary. Type: {type(pivot_event_data)}")
        return "Error: Parsed pivot event data did not result in a dictionary."

    pivot_input_timestamp_str = pivot_event_data.get("timestamp")
    print(f"[DEBUG] Value of 'timestamp' field from PIVOT event data: {pivot_input_timestamp_str!r}")

    if not pivot_input_timestamp_str:
        print(f"[ERROR] 'timestamp' key not found in PIVOT event data or its value is empty/None. Keys: {list(pivot_event_data.keys())}")
        return "Error: Pivot event JSON does not contain a 'timestamp' field or it's empty."

    try:
        pivot_dt = datetime.fromisoformat(pivot_input_timestamp_str)
        if pivot_dt.tzinfo is None:
            print(f"[WARNING] Pivot timestamp '{pivot_input_timestamp_str}' was naive. Assuming UTC.")
            pivot_dt = pivot_dt.replace(tzinfo=timezone.utc)
    except ValueError as e:
        print(f"[ERROR] Parsing pivot timestamp string '{pivot_input_timestamp_str}' failed: {e}")
        return f"Error: Invalid ISO 8601 format for pivot timestamp: '{pivot_input_timestamp_str}'."
    except TypeError as e:
        print(f"[ERROR] TypeError parsing pivot timestamp (was None or not string?): '{pivot_input_timestamp_str}'. Error: {e}")
        return "Error: Timestamp field from pivot event was invalid for parsing."


    window_start_dt = pivot_dt - timedelta(seconds=window_seconds_before)
    window_end_dt = pivot_dt + timedelta(seconds=window_seconds_after)
    print(f"[INFO] Calculated time window: Start={window_start_dt.isoformat()}, End={window_end_dt.isoformat()}")

    if not LOG_FILE_PATH.exists():
        print(f"[ERROR] Log file not found at {LOG_FILE_PATH}")
        return "Error: Log file not found."

    print(f"[INFO] Reading log file: {LOG_FILE_PATH}")
    processed_lines = 0
    found_relevant_count = 0
    with open(LOG_FILE_PATH, 'r') as f:
        for line_number, line in enumerate(f, 1):
            processed_lines += 1
            try:
                log_entry = json.loads(line)
            except json.JSONDecodeError:
                continue

            log_file_entry_ts_str = log_entry.get("timestamp")

            if not log_file_entry_ts_str:
                continue

            try:
                log_dt = datetime.fromisoformat(log_file_entry_ts_str)
                if log_dt.tzinfo is None:
                    log_dt = log_dt.replace(tzinfo=timezone.utc)
            except ValueError:
                continue

            if window_start_dt <= log_dt <= window_end_dt:
                relevant_log_entries.append(line.strip())
                found_relevant_count +=1

    print(f"[INFO] Processed {processed_lines} lines from log file.")
    if not relevant_log_entries:
        print(f"[INFO] No relevant log entries found for pivot {pivot_dt.isoformat()} in window.")
        return f"No relevant log entries found for pivot {pivot_dt.isoformat()} within the time window."

    print(f"[INFO] Found {found_relevant_count} relevant entries.")
    return "\n".join(relevant_log_entries)

tools = [get_relevant_logs_for_event]

# ----------------------------------------------------------------------
# Prompt
# ----------------------------------------------------------------------
prompt_template = """
You are an incident analysis agent for a manufacturing telemetry system. Your job is to analyze telemetry alert logs and return a final incident report in structured JSON format.

When you receive an event as input (this will be a JSON string), you should use its timestamp to fetch relevant surrounding logs.

You have access to the following tools:
{tools}

Use the following format:

Question: the input question you must answer (this will be the triggering event JSON)
Thought: I have received a trigger event. I need to extract its timestamp and use it to get relevant logs using the 'get_relevant_logs_for_event' tool. The entire input event JSON should be passed as 'pivot_event_json_str' to the tool.
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action. For 'get_relevant_logs_for_event', this will be a JSON object like: {{"pivot_event_json_str": "...", "window_seconds_before": 30, "window_seconds_after": 5}}
Observation: the result of the action (this will be a string of relevant log entries, or an error message)
Thought: Now I have the relevant logs (or an error if none were found). I will analyze these logs to construct the incident report. If no logs were found or there was an error, I should indicate this in the report or ask for clarification.
... (further thought process to build the report based on the observation) ...

Thought: I now know the final answer
Final Answer: {{
  "id": "placeholder_uuid_will_be_replaced",
  "display_id": "placeholder_INC-XXX_will_be_replaced",
  "title": "Brief summary based on logs around the pivot event",
  "status": "in progress",
  "severity": "high",
  "assigned_team": "Maintenance Team",
  "reported_at": "YYYY-MM-DDTHH:MM:SSZ", // Should be based on the pivot event or earliest relevant log
  "description": "Detailed description of the incident based on the observed relevant logs. Mention the pivot event and surrounding sensor anomalies or KPI changes.",
  "updates": ["Log analysis initiated based on pivot event.", "Relevant logs from window X to Y fetched."]
}}

Do not wrap the response in markdown or add explanations.

Begin!

Question: {input}
{agent_scratchpad}
"""

prompt = PromptTemplate.from_template(prompt_template)

# ----------------------------------------------------------------------
# Agent setup (no changes needed here)
# ----------------------------------------------------------------------
print(f"[DEBUG] Using OPENAI_API_KEY: {settings.api.agent.OPENAI_API_KEY}")
llm = ChatOpenAI(
    temperature=0.2,
    model="gpt-4o-2024-08-06",
    api_key=settings.api.agent.OPENAI_API_KEY or os.getenv("OPENAI_API_KEY")
)

agent_runnable = create_react_agent(llm=llm, tools=tools, prompt=prompt)
_agent_executor = AgentExecutor(agent=agent_runnable, tools=tools, verbose=True,handle_parsing_errors=True)

# ----------------------------------------------------------------------
# Post-processing helpers (no changes needed here)
# ----------------------------------------------------------------------

def extract_json_from_output(output: str) -> dict:
    """Extract clean JSON from agent output, handling formatting noise."""
    text = output.split("Final Answer:")[-1].strip()

    if text.startswith("```json"):
        text = text[len("```json"):].strip()
    if text.endswith("```"):
        text = text[:-3].strip()

    text = re.sub(r"\*.*?\*", "", text).strip()

    return json.loads(text)

# ----------------------------------------------------------------------
# Public agent runner (MAJOR CHANGE HERE)
# ----------------------------------------------------------------------

def run_incident_analysis(prompt_text: str, max_retries: int = 3) -> IncidentReportModel:
    """
    Runs the agent with retries and validated structured output,
    injecting a unique ID and sequential display ID before returning.
    """
    chat_history = ""
    current_prompt = prompt_text

    for attempt in range(1, max_retries + 1):
        print(f"\n--- Attempt {attempt} ---")
        result = _agent_executor.invoke({
            "input": current_prompt,
            "chat_history": chat_history
        })

        output = result.get("output", "")
        chat_history += f"\nUser: {current_prompt}\nAgent: {output}"

        try:
            parsed_llm_output = extract_json_from_output(output)
            
            # --- Inject unique IDs here ---
            parsed_llm_output["id"] = str(uuid.uuid4()) # Generate a globally unique ID
            parsed_llm_output["display_id"] = generate_next_display_id() # Generate the sequential human-readable ID
            # --- End ID injection ---

            validated = IncidentReportModel(**parsed_llm_output)
            return validated  # ✅ All good

        except Exception as e:
            print(f"❌ Validation or ID injection failed: {e}")
            current_prompt = (
                "Your previous response was not valid JSON or did not match the schema. "
                "Please regenerate just the Final Answer as a clean JSON object using the correct format. "
                "Ensure all required fields (title, status, severity, assigned_team, reported_at, description, updates) are present."
                # Add a reminder to the LLM not to worry about `id` or `display_id`
                "Note: The 'id' and 'display_id' fields will be generated by the system, just provide valid content for the other fields."
            )

    raise RuntimeError("❌ Failed to generate a valid incident report after retries.")

# ----------------------------------------------------------------------
# CLI runner (no changes needed here)
# ----------------------------------------------------------------------

if __name__ == "__main__":
    # Example usage: Replace this with an actual telemetry event JSON if you have one
    sample_trigger_event = {
        "timestamp": "2025-05-22T10:00:00Z",
        "sensor_id": "S123",
        "metric": "temperature",
        "value": 150.5,
        "unit": "C",
        "alert_level": "critical",
        "message": "Temperature exceeded critical threshold on sensor S123."
    }
    trigger_event_json_str = json.dumps(sample_trigger_event)

    try:
        incident = run_incident_analysis(
            f"Analyze the following telemetry alert and provide a structured incident report: {trigger_event_json_str}",
            max_retries=2 # Increased retries just in case LLM is slow
        )
        print("\n✅ Final validated incident:")
        print(incident.model_dump_json(indent=2)) # Use model_dump_json for pretty printing Pydantic models
    except Exception as err:
        print("🚨 Final failure:", err)