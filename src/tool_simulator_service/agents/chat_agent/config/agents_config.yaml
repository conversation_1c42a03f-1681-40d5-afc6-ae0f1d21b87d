#Basic model configuration
BASIC_MODEL:
  model: "gpt-4o"
  temperature: 0.2
  base_url: "https://api.openai.com/v1"
  api_key: "$OPENAI_API_KEY"  # Read from environment for security
  max_tokens: 6144
  timeout: 30

# You can keep the others for future use
REASONING_MODEL:
  model: "gpt-3.5-turbo"
  temperature: 0.2
  base_url: "https://api.openai.com/v1"
  api_key: "$OPENAI_API_KEY"
  max_tokens: 2048
  timeout: 30

VISION_MODEL:
  model: "gpt-4o"
  temperature: 0.2
  base_url: "https://api.openai.com/v1"
  api_key: "$OPENAI_API_KEY"
  max_tokens: 2048
  timeout: 30
