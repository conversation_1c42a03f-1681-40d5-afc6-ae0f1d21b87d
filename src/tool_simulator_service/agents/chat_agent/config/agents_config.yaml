#Basic model configuration
BASIC_MODEL:
  model: "gpt-4o"
  temperature: 0.2
  base_url: "https://api.openai.com/v1"
  api_key: "${OPENAI_API_KEY}"  # Read from environment for security
  max_tokens: 6144
  timeout: 30

# You can keep the others for future use
REASONING_MODEL:
  model: "gpt-3.5-turbo"
  temperature: 0.2
  base_url: "https://api.openai.com/v1"
  api_key: "$OPENAI_API_KEY"
  max_tokens: 2048
  timeout: 30

VISION_MODEL:
  model: "gpt-4o"
  temperature: 0.2
  base_url: "https://api.openai.com/v1"
  api_key: "$OPENAI_API_KEY"
  max_tokens: 2048
  timeout: 30

RAG_CONFIG:
  # Directory containing documents to process
  data_dir: "src/tool_simulator_service/agents/chat_agent/rag/data"
  
  # Vector store configuration
  vector_store:
    persist_dir: "vector_store"
    embedding_model: "text-embedding-3-small"
    embedding_kwargs:
      model: "text-embedding-3-small"
      openai_api_key: "${OPENAI_API_KEY}"  # Will be read from environment
  
  # Text chunking configuration
  chunking:
    strategy: "recursive"  # Options: simple, recursive, markdown
    chunk_size: 1000
    chunk_overlap: 200
  
  # Logging configuration
  logging:
    level: "INFO"
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Event logs configuration
EVENT_LOGS:
  # Path to the events log file (JSONL format)
  log_file: "src/tool_simulator_service/agents/incident_agent/logs/events.jsonl"
  
  # Default time window for event search (in seconds)
  default_window_seconds: 30
  
  # Maximum number of events to return by default
  default_max_results: 10
  
  # Field names in the log entries
  field_names:
    timestamp: "timestamp"  # Field containing the timestamp
    level: "level"         # Field containing the log level
    message: "message"     # Field containing the log message
    source: "source"       # Field containing the source identifier

