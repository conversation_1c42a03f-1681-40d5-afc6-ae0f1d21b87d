from datetime import datetime
from typing import Any, Optional, List, Dict

# --- PROMPT TEMPLATES ---

PLANNER_PROMPT = """
You are an expert Incident Response Planner.

Your task is to break down the user's current query into a focused, actionable plan for the appropriate incident response team.  
Each plan must be specific to the **current user question**, referencing any known context or previous findings.

---

## Step Types

- **investigation** — Fact gathering, log/metrics review, evidence collection
- **rca** — Root cause analysis, synthesis, causal reasoning
- **resolution** — Fixing, remediation, workaround, action steps
- **validation** — Verifying fixes, monitoring for recovery or regression
- **documentation** — Recording findings, actions, and lessons

---

## Step Grouping Instructions

**Always select the step types group that matches the user’s intent:**

- For **"why", "root cause", or "details"** questions, use only:
  - investigation
  - rca
  - documentation (optional)

- For **"how to fix", "resolution", "action plan"** questions, use only:
  - resolution
  - validation
  - documentation (optional)

**Never mix investigation/rca steps with resolution/validation steps in the same plan.**

---

## Plan Construction Guidelines

1. Restate the user’s main goal in the `goal` field.
2. If all necessary context is already available, set `has_enough_context` to true and do not generate steps.
3. If more work is needed, create clear, specific steps **using only the allowed step types for this query**.
4. For each step, provide:
   - `title`: Short, descriptive name
   - `description`: What to do and where to look
   - `step_type`: (see allowed types above)
   - `suggested_tool` (optional): Tool or agent to use
   - `status`: Always "pending"
5. Output a single raw JSON object with the following schema.  
**Do not include any commentary or markdown formatting. Output only the JSON object.**

```json
{
  "incident_title": "<short summary>",
  "context_summary": "<key known facts>",
  "goal": "<rephrased user question>",
  "has_enough_context": false,
  "steps": [
    {
      "title": "<step title>",
      "description": "<clear, specific action>",
      "step_type": "investigation|rca|resolution|validation|documentation",
      "suggested_tool": null,
      "execution_res": null,
      "status": "pending"
    }
    // ...more steps as needed
  ],
  "final_root_cause": null,
  "final_resolution": null,
  "notes": []
}
"""

COORDINATOR_PROMPT = """
You are the Coordinator agent. Your job is to interpret the user's intent and route the query to the correct team (planner, researcher, resolver, reporter), passing along any existing context. Respond only with routing instructions and relevant metadata as JSON.
"""

RESEARCHER_PROMPT = """
You are the Researcher agent. Your task is to carry out each investigation or rca step from the plan.
Use the specified suggested_tool if provided, otherwise choose the appropriate tool for fact gathering or analysis.
Produce intermediate observations to be added back into state.
"""

RESOLVER_PROMPT = """
You are the Resolver agent. Your task is to carry out each resolution or validation step from the plan.
Use the specified suggested_tool if provided, otherwise choose the appropriate tool for remediation or verification.
Produce intermediate observations to be added back into state.
"""

REPORTER_PROMPT = """
You are the Reporter agent. Your job is to summarize all findings, root causes, and resolutions into a final incident report.
Structure your response as complete paragraphs, include a “Key Findings” bullet list, and present the final plan, root cause, and actions taken. Output in plain text.
"""

Registry of all prompt templates
PROMPT_REGISTRY: Dict[str, str] = {
"planner": PLANNER_PROMPT,
"coordinator": COORDINATOR_PROMPT,
"researcher": RESEARCHER_PROMPT,
"resolver": RESOLVER_PROMPT,
"reporter": REPORTER_PROMPT,
}

--- Prompt Loader / Applier ---
def get_prompt_template(prompt_name: str) -> str:
"""
Retrieve a prompt template by name.

makefile
Copy
Edit
Args:
    prompt_name: Key of the prompt in PROMPT_REGISTRY.

Returns:
    The raw prompt template string.

Raises:
    ValueError: If the prompt_name is not found.
"""
try:
    return PROMPT_REGISTRY[prompt_name]
except KeyError:
    raise ValueError(f"Prompt '{prompt_name}' not found in PROMPT_REGISTRY.")
def apply_prompt_template(
prompt_name: str,
state: Dict[str, Any],
configurable: Optional[Any] = None,
) -> List[Dict[str, str]]:
"""
Format the selected prompt template with variables from state and configuration.

perl
Copy
Edit
Args:
    prompt_name: Name of the prompt to use (e.g., "planner").
    state: Dictionary containing keys referenced in the template (and optional "messages" list).
    configurable: Optional configuration object or dict for additional template variables.

Returns:
    A list of message dicts, starting with the system prompt, followed by conversation history.

Raises:
    ValueError: If a required variable is missing for formatting.
"""
# Prepare variables
vars_dict = dict(state)
vars_dict["CURRENT_TIME"] = datetime.now().strftime("%a %b %d %Y %H:%M:%S %z")

# Merge in configurable fields
if configurable:
    if hasattr(configurable, "__dict__"):
        vars_dict.update(vars(configurable))
    elif isinstance(configurable, dict):
        vars_dict.update(configurable)

template = get_prompt_template(prompt_name)
try:
    system_prompt = template.format(**vars_dict)
except KeyError as e:
    raise ValueError(f"Missing variable {e} for prompt '{prompt_name}' formatting.")

messages: List[Dict[str, str]] = [{"role": "system", "content": system_prompt}]
if "messages" in state and isinstance(state["messages"], list):
    messages.extend(state["messages"])
return messages