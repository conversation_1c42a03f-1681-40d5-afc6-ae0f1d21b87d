"""Incident Planner Model.

Defines structured models for incident analysis, RCA, and resolution planning steps.
"""

from enum import Enum
from typing import List, Optional
from pydantic import BaseModel, Field


class StepType(str, Enum):
    """Types of steps in the incident response plan."""
    INVESTIGATION = "investigation"
    RCA = "rca"
    RESOLUTION = "resolution"
    VALIDATION = "validation"
    DOCUMENTATION = "documentation"


class Step(BaseModel):
    """A single actionable step in the plan."""
    title: str = Field(..., description="Short title of the step.")
    description: str = Field(..., description="Detailed action or analysis required.")
    step_type: StepType = Field(..., description="Type of step (e.g. RCA, RESOLUTION, etc.)")
    suggested_tool: Optional[str] = Field(None, description="Name of the tool/agent best suited for this step.")
    execution_res: Optional[str] = Field(None, description="Result/output after executing this step.")
    status: Optional[str] = Field("pending", description="Execution status: pending, in_progress, done, skipped.")

class Plan(BaseModel):
    """A structured incident/RCA/resolution plan."""
    incident_title: str = Field(..., description="Brief summary/title of the incident.")
    context_summary: Optional[str] = Field(None, description="Key context or background about the incident.")
    goal: str = Field(..., description="Main goal or objective for the plan.")
    has_enough_context: bool = Field(False, description="Whether enough context exists to proceed.")
    steps: List[Step] = Field(default_factory=list, description="Steps for investigation, RCA, and resolution.")
    final_root_cause: Optional[str] = Field(None, description="Root cause, after investigation.")
    final_resolution: Optional[str] = Field(None, description="Final resolution steps, if determined.")
    notes: Optional[List[str]] = Field(default_factory=list, description="Any extra notes or findings.")
    assigned_team: Optional[str] = Field(None, description="Team or individual responsible for executing the plan.")

    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "incident_title": "Service Outage on Payment Gateway",
                    "context_summary": "Payment gateway 500 errors for all customers. Correlated with deployment at 12:05 UTC.",
                    "goal": "Identify root cause and restore service ASAP.",
                    "has_enough_context": False,
                    "steps": [
                        {
                            "title": "Log Analysis",
                            "description": "Examine error logs from affected pods and correlate with deployment timestamp.",
                            "step_type": "investigation",
                            "suggested_tool": "log_analyzer",
                            "execution_res": None,
                            "status": "pending"
                        },
                        {
                            "title": "Root Cause Identification",
                            "description": "Analyze findings to determine root cause.",
                            "step_type": "rca",
                            "suggested_tool": "rca_agent",
                            "execution_res": None,
                            "status": "pending"
                        },
                        {
                            "title": "Rollback Deployment",
                            "description": "Initiate rollback to previous stable deployment.",
                            "step_type": "resolution",
                            "suggested_tool": "orchestration_tool",
                            "execution_res": None,
                            "status": "pending"
                        }
                    ],
                    "final_root_cause": None,
                    "final_resolution": None,
                    "notes": []
                }
            ]
        }

