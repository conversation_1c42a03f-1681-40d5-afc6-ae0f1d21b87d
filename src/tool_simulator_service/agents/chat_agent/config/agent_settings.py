import os
import yaml
from typing import Dict, Any, Literal
from langchain_openai import ChatOpenAI
from pathlib import Path
from langchain_core.runnables import RunnableConfig

def get_config_path(relative_path: str = "agents_config.yaml") -> str:
    """Get absolute path to config file regardless of where code is run from."""
    # Get the directory where this settings.py file is located
    base_dir = Path(__file__).parent.absolute()
    return str(base_dir / relative_path)

# ------------- 1. LLM Type Definitions -------------

LLMType = Literal["basic", "reasoning", "vision"]

# ------------- 2. Agent to LLM Type Mapping -------------

AGENT_LLM_MAP: dict[str, LLMType] = {
    "coordinator": "basic",
    "planner": "basic",
    "researcher": "basic",
    "resolver": "basic",      # renamed for clarity
    "reporter": "basic",
    # Extend as needed for other agent roles...
}

# ------------- 3. Config Loader (with env support) -------------

_config_cache: Dict[str, Dict[str, Any]] = {}

def _expand_env(value: str) -> str:
    """Replace $ENV_VAR with environment variable value."""
    if isinstance(value, str) and value.startswith("$"):
        env_var = value[1:]
        env_val = os.getenv(env_var)
        if env_val is None:
            raise ValueError(f"Environment variable '{env_var}' is not set")
        return env_val
    return value

def _process_dict(d: dict) -> dict:
    """Recursively expand env vars in all string values."""
    result = {}
    for k, v in d.items():
        if isinstance(v, dict):
            result[k] = _process_dict(v)
        elif isinstance(v, str):
            result[k] = _expand_env(v)
        else:
            result[k] = v
    return result

def load_yaml_config(file_path: str) -> Dict[str, Any]:
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Config file not found: {file_path}")
    if file_path in _config_cache:
        return _config_cache[file_path]
    with open(file_path, "r") as f:
        raw = yaml.safe_load(f)
    processed = _process_dict(raw)
    _config_cache[file_path] = processed
    return processed

# ------------- 4. LLM Instance Factory (Singleton/Cached) -------------

_llm_cache: dict[str, ChatOpenAI] = {}

def get_llm_by_type(llm_type: LLMType, config_path: str = None) -> ChatOpenAI:
    """
    Get a cached LLM instance by type.
    """
    if config_path is None:
        config_path = get_config_path("agents_config.yaml")
        
    if llm_type in _llm_cache:
        return _llm_cache[llm_type]
    
    conf = load_yaml_config(config_path)
    key_map = {
        "basic": "BASIC_MODEL",
        "reasoning": "REASONING_MODEL",
        "vision": "VISION_MODEL",
    }
    model_conf = conf.get(key_map[llm_type])
    if not model_conf:
        raise ValueError(f"No config found for LLM type '{llm_type}' in {config_path}")
    
    # Validate required fields for BASIC_MODEL (add/remove as needed)
    required_fields = ["model", "temperature", "base_url", "api_key", "max_tokens", "timeout"]
    for field in required_fields:
        if field not in model_conf or model_conf[field] in (None, ""):
            raise ValueError(f"Missing required field '{field}' in {llm_type} model config")
    
    llm = ChatOpenAI(
        **model_conf
    )
    _llm_cache[llm_type] = llm
    return llm

# ------------- 5. Utility: Get Agent's LLM ------------- 

def get_llm_for_agent(agent_name: str, config_path: str = None) -> ChatOpenAI:
    if config_path is None:
        config_path = get_config_path("agents_config.yaml")
        
    llm_type = AGENT_LLM_MAP.get(agent_name)
    if not llm_type:
        raise ValueError(f"No LLM type mapped for agent '{agent_name}'")
    return get_llm_by_type(llm_type, config_path)


# ------------- 6. Chat Agent Configuration -------------
import os
from dataclasses import dataclass, field, fields
from typing import Any, Optional

from langchain_core.runnables import RunnableConfig

@dataclass(kw_only=True)
class ChatAgentConfiguration:
    """
    Central configuration for incident chat agent workflow.
    """
    max_plan_iterations: int = 2  # e.g., retry planner at most twice
    max_step_num: int = 5         # e.g., at most 5 steps in a plan
    team_timeout: int = 60        # e.g., seconds to wait per team node
    enable_doc_steps: bool = True # optionally enable/disable documentation steps
    # Add more fields as needed!

    @classmethod
    def from_runnable_config(
        cls, config: Optional[RunnableConfig] = None
    ) -> "ChatAgentConfiguration":
        configurable = (
            config["configurable"] if config and "configurable" in config else {}
        )
        values: dict[str, Any] = {
            f.name: os.environ.get(f.name.upper(), configurable.get(f.name))
            for f in fields(cls)
            if f.init
        }
        # Cast values to correct types
        for f in fields(cls):
            if f.name in values:
                if f.type is bool:
                    values[f.name] = str(values[f.name]).lower() == "true"
                elif f.type is int:
                    values[f.name] = int(values[f.name])
        return cls(**{k: v for k, v in values.items() if v is not None})


# ------------- 6. Example Usage -------------
if __name__ == "__main__":
    os.environ["OPENAI_API_KEY"] = "sk-xxxxxxx"  # for demo only, use env or .env in real cases
    llm = get_llm_for_agent("planner")
    print(llm.invoke("Hello, are you ready?"))
