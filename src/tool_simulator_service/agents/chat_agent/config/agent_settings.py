import os
import yaml
from typing import Dict, Any, Literal
from langchain_openai import ChatOpenAI
from pathlib import Path
from langchain_core.runnables import RunnableConfig

def get_config_path(relative_path: str = "agents_config.yaml") -> str:
    """Get absolute path to config file regardless of where code is run from."""
    # Get the directory where this settings.py file is located
    base_dir = Path(__file__).parent.absolute()
    return str(base_dir / relative_path)

# ------------- 1. LLM Type Definitions -------------

LLMType = Literal["basic", "reasoning", "vision"]

# ------------- 2. Agent to LLM Type Mapping -------------

AGENT_LLM_MAP: dict[str, LLMType] = {
    "coordinator": "basic",
    "planner": "basic",
    "researcher": "basic",
    "resolver": "basic",      # renamed for clarity
    "reporter": "basic",
    # Extend as needed for other agent roles...
}

# ------------- 3. Config Loader (with env support) -------------

_config_cache: Dict[str, Dict[str, Any]] = {}

def _expand_env(value: str) -> str:
    """Replace $ENV_VAR or ${ENV_VAR} with environment variable value."""
    if not isinstance(value, str):
        return value
        
    # Handle ${VAR} syntax
    if value.startswith("${") and value.endswith("}"):
        env_var = value[2:-1]
        env_val = os.getenv(env_var)
        if env_val is None:
            raise ValueError(f"Environment variable '{env_var}' is not set")
        return env_val
    # Handle $VAR syntax
    elif value.startswith("$"):
        env_var = value[1:]
        env_val = os.getenv(env_var)
        if env_val is None:
            raise ValueError(f"Environment variable '{env_var}' is not set")
        return env_val
    return value

def _process_dict(d: dict) -> dict:
    """Recursively expand env vars in all string values."""
    result = {}
    for k, v in d.items():
        if isinstance(v, dict):
            result[k] = _process_dict(v)
        elif isinstance(v, str):
            result[k] = _expand_env(v)
        else:
            result[k] = v
    return result

def load_yaml_config(file_path: str) -> Dict[str, Any]:
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Config file not found: {file_path}")
    if file_path in _config_cache:
        return _config_cache[file_path]
    with open(file_path, "r") as f:
        raw = yaml.safe_load(f)
    processed = _process_dict(raw)
    _config_cache[file_path] = processed
    return processed

# ------------- 4. LLM Instance Factory (Singleton/Cached) -------------

_llm_cache: dict[str, ChatOpenAI] = {}

def get_llm_by_type(llm_type: LLMType, config_path: str = None) -> ChatOpenAI:
    """
    Get a cached LLM instance by type.
    """
    if config_path is None:
        config_path = get_config_path("agents_config.yaml")
        
    if llm_type in _llm_cache:
        return _llm_cache[llm_type]
    
    conf = load_yaml_config(config_path)
    key_map = {
        "basic": "BASIC_MODEL",
        "reasoning": "REASONING_MODEL",
        "vision": "VISION_MODEL",
    }
    model_conf = conf.get(key_map[llm_type])
    if not model_conf:
        raise ValueError(f"No config found for LLM type '{llm_type}' in {config_path}")
    
    # Validate required fields for BASIC_MODEL (add/remove as needed)
    required_fields = ["model", "temperature", "base_url", "api_key", "max_tokens", "timeout"]
    for field in required_fields:
        if field not in model_conf or model_conf[field] in (None, ""):
            raise ValueError(f"Missing required field '{field}' in {llm_type} model config")
    
    llm = ChatOpenAI(
        **model_conf
    )
    _llm_cache[llm_type] = llm
    return llm

def get_rag_config(config_path: str = None) -> Dict[str, Any]:
    """Get the RAG configuration."""
    config = load_yaml_config(config_path or get_config_path())
    return config.get("RAG_CONFIG", {})

def get_event_logs_config(config_path: str = None) -> Dict[str, Any]:
    """
    Get the event logs configuration.
    
    Args:
        config_path: Path to the config file. If None, uses default location.
        
    Returns:
        Dictionary with event logs configuration
    """
    config = load_yaml_config(config_path or get_config_path())
    return config.get("EVENT_LOGS", {})

# ------------- 5. Utility: Get Agent's LLM ------------- 

def get_llm_for_agent(agent_name: str, config_path: str = None) -> ChatOpenAI:
    if config_path is None:
        config_path = get_config_path("agents_config.yaml")
        
    llm_type = AGENT_LLM_MAP.get(agent_name)
    if not llm_type:
        raise ValueError(f"No LLM type mapped for agent '{agent_name}'")
    return get_llm_by_type(llm_type, config_path)


# ------------- 6. Chat Agent Configuration -------------
import os
from dataclasses import dataclass, field, fields
from typing import Any, Optional

from langchain_core.runnables import RunnableConfig

@dataclass(kw_only=True)
class ChatAgentConfiguration:
    """
    Central configuration for incident chat agent workflow.
    """
    max_plan_iterations: int = 2  # e.g., retry planner at most twice
    max_step_num: int = 5         # e.g., at most 5 steps in a plan
    team_timeout: int = 60        # e.g., seconds to wait per team node
    enable_doc_steps: bool = True # optionally enable/disable documentation steps
    # Add more fields as needed!

    @classmethod
    def from_runnable_config(
        cls, config: Optional[RunnableConfig] = None
    ) -> 'ChatAgentConfiguration':
        # Default values
        default_values = {
            'max_plan_iterations': 3,
            'max_step_num': 5,
            'team_timeout': 60,
            'enable_doc_steps': True
        }
        
        # Get values from config, with fallback to defaults
        values = {}
        for f in fields(cls):
            if f.init:
                # Get value from config or use default
                config_value = (config or {}).get(f.name)
                if config_value is not None:
                    values[f.name] = config_value
                else:
                    values[f.name] = default_values.get(f.name, getattr(cls, f.name, None))
        
        # Cast values to correct types with proper error handling
        for f in fields(cls):
            if f.name in values and values[f.name] is not None:
                try:
                    if f.type is bool:
                        if isinstance(values[f.name], str):
                            values[f.name] = values[f.name].lower() == "true"
                        else:
                            values[f.name] = bool(values[f.name])
                    elif f.type is int:
                        values[f.name] = int(values[f.name]) if str(values[f.name]).strip() else 0
                except (ValueError, TypeError) as e:
                    logger.warning(f"Error casting {f.name} to {f.type}: {e}")
                    values[f.name] = default_values.get(f.name, 0 if f.type is int else False)
        
        return cls(**values)


# ------------- 6. Example Usage -------------
if __name__ == "__main__":
    os.environ["OPENAI_API_KEY"] = "sk-xxxxxxx"  # for demo only, use env or .env in real cases
    llm = get_llm_for_agent("planner")
    print(llm.invoke("Hello, are you ready?"))
