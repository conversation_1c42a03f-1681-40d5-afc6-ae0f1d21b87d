"""
Interactive chat interface for testing the agent using the 'rich' library.

Usage:
    # For a clean, human-readable chat experience:
    python -m src.tool_simulator_service.agents.chat_agent.test_chat_rich

    # To see all the detailed debug logs (the "shielded" info):
    python -m src.tool_simulator_service.agents.chat_agent.test_chat_rich --debug
"""
import asyncio
import argparse
from typing import Dict, Any, List

from rich.console import Console
from rich.panel import Panel
from rich.markdown import Markdown
from rich.rule import Rule

from langchain_core.messages import HumanMessage, AIMessage, ToolMessage
from src.tool_simulator_service.agents.chat_agent.graph.builder import build_graph, build_graph_with_memory
from src.tool_simulator_service.agents.chat_agent.graph.state import ChatAgentState
import uuid
import time
# --- Setup Rich Console and Argument Parser ---
console = Console()
parser = argparse.ArgumentParser(description="Rich Chat UI for LangGraph Agent")
parser.add_argument(
    "--debug",
    action="store_true",
    help="Enable detailed debug logging to the console."
)
args = parser.parse_args()


def print_debug(message: str, data: Any = None):
    """Prints a debug message only if --debug is enabled."""
    if args.debug:
        if data:
            console.log(f"[yellow]{message}[/yellow]", data)
        else:
            console.log(f"[yellow]{message}[/yellow]")


async def stream_graph_updates(user_input: str, state: Dict[str, Any] = None):
    """Initializes or updates state and streams graph execution, printing only key outputs."""
    print_debug("--- Starting stream_graph_updates ---")
    
    # 1. Initialize or update the state (logic is the same as before)
    if state is None:
        print_debug("Initializing new state")
        state = {
            "messages": [HumanMessage(content=user_input)],
            "observations": [],
            "plan_iterations": 0,
            "current_plan": None,
            "plan_approved": False,
            "plan_approval_ts": None,
            "plan_feedback": None,
            "error": None,
            "step": "coordinator",  # <-- The crucial missing key
            "auto_accepted_plan": False,
            "user_query": user_input,
            "incident_context": {
                "environment": "production",
                "severity": "medium",
                "service": "unknown"
            },
            "user_feedback": None,
            "assigned_team": None,
            "current_step": 0,
            "max_steps": 5,
            "execution_results": [],
            # Add configurable keys for checkpointer
            "configurable": {
                "thread_id": f"thread_{int(time.time())}",
                "checkpoint_ns": "test_session"
            }
        }
    else:
        print_debug("Updating existing state")
        if "messages" in state and isinstance(state["messages"], list):
            state["messages"].append(HumanMessage(content=user_input))
        else:
            state["messages"] = [HumanMessage(content=user_input)]

    # Use a validated ChatAgentState object for the graph
    try:
        agent_state = ChatAgentState(**state)
        print_debug("Successfully created ChatAgentState object.")
    except Exception as e:
        console.print(Panel(f"[bold red]Error:[/bold red] Failed to create agent state: {e}", title="STATE ERROR", border_style="red"))
        return state

    # --- Stream the graph and handle output ---
    final_ai_response = ""
    
    # Initialize the graph with memory
    graph = build_graph_with_memory()
    
    try:
        # Show a "thinking" spinner while the graph runs
        with console.status("[bold green]Assistant is thinking...", spinner="dots"):
            async for event in graph.astream(
                agent_state, 
                stream_mode="values", 
                config={"configurable": {"thread_id": agent_state.get("configurable", {}).get("thread_id")}}
            ):
                print_debug("Received event:", event)

                # Check for interruptions (like the human_feedback_node)
                if "__interrupt__" in event:
                    # Get the first element from the tuple, then its value
                    interrupt_value = event["__interrupt__"][0].value
                    final_ai_response += f"\n\n{interrupt_value}"
                    print_debug("Graph interrupted, waiting for user feedback.")
                # Look for the last AIMessage to use as the response
                messages = event.get("messages")
                if messages and isinstance(messages, list):
                    last_message = messages[-1]
                    if isinstance(last_message, AIMessage) and last_message.content:
                        final_ai_response = last_message.content

                # Update state in the background
                for key, value in event.items():
                    if value is not None:
                        state[key] = value

    except Exception as e:
        console.print(Panel(f"[bold red]An unexpected error occurred:[/bold red]\n{e}", title="EXECUTION ERROR", border_style="red"))
        import traceback
        print_debug("Traceback:", traceback.format_exc())

        # Print the final, formatted AI response
    final_report = state.get("final_report")

    # Determine what to print: the final report has priority
    response_to_print = final_report if final_report else final_ai_response

    if response_to_print:
        console.print(
            Panel(
                Markdown(response_to_print),
                title="Assistant",
                border_style="blue",
                style="blue"
            )
        )
    else:
        print_debug("Graph finished with no new AIMessage or final_report to display.")
        
        print_debug("--- Graph execution completed ---")
        return state


async def main():
    """Main function to run the interactive chat loop."""
    console.print(Rule("[bold cyan]Chat Agent Test Interface[/bold cyan]"))
    console.print("Type 'quit', 'exit', or 'q' to end the session.\n")
    
    state = None
    
    while True:
        try:
            user_input = console.input("[bold green]You: [/bold green]").strip()
            if not user_input:
                continue
                
            if user_input.lower() in ["quit", "exit", "q"]:
                console.print("\n[bold cyan]Goodbye![/bold cyan]")
                break

            # Process the input and get the updated state
            state = await stream_graph_updates(user_input, state)
            
        except (KeyboardInterrupt, asyncio.CancelledError):
            console.print("\n\n[bold cyan]Session interrupted. Goodbye![/bold cyan]")
            break
        except Exception as e:
            console.print(Panel(f"[bold red]A critical error occurred in the main loop:[/bold red]\n{e}", title="CRITICAL ERROR", border_style="red"))


if __name__ == "__main__":
    asyncio.run(main())