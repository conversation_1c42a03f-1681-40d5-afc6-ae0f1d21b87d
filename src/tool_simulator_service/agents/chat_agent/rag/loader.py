from pathlib import Path
from typing import List, Union, Optional, Dict, Any
import logging

# Third-party imports
try:
    from PyPDF2 import Pdf<PERSON>eader
    from docx import Document
    import pandas as pd
except ImportError:
    logging.warning("Some required packages are missing. Please install them using: pip install PyPDF2 python-docx pandas")

class DocumentLoader:
    """
    Handles loading of different document types and extracting their text content.
    """
    
    @staticmethod
    def load_file(file_path: Union[str, Path]) -> Optional[str]:
        """
        Load and extract text from a file based on its extension.
        
        Args:
            file_path: Path to the file to load
            
        Returns:
            Extracted text content or None if loading fails
        """
        path = Path(file_path)
        if not path.exists():
            logging.error(f"File not found: {file_path}")
            return None
            
        try:
            if path.suffix.lower() == '.txt':
                return DocumentLoader._load_txt(path)
            elif path.suffix.lower() == '.pdf':
                return DocumentLoader._load_pdf(path)
            elif path.suffix.lower() == '.docx':
                return DocumentLoader._load_docx(path)
            elif path.suffix.lower() in ['.csv', '.xlsx', '.xls']:
                return DocumentLoader._load_tabular(path)
            else:
                logging.warning(f"Unsupported file type: {path.suffix}")
                return None
        except Exception as e:
            logging.error(f"Error loading file {file_path}: {str(e)}")
            return None
    
    @staticmethod
    def _load_txt(file_path: Path) -> str:
        """Load text from a .txt file."""
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    @staticmethod
    def _load_pdf(file_path: Path) -> str:
        """Extract text from a PDF file."""
        reader = PdfReader(file_path)
        text = []
        for page in reader.pages:
            text.append(page.extract_text() or "")
        return "\n".join(text)
    
    @staticmethod
    def _load_docx(file_path: Path) -> str:
        """Extract text from a Word document."""
        doc = Document(file_path)
        return "\n".join([para.text for para in doc.paragraphs])
    
    @staticmethod
    def _load_tabular(file_path: Path) -> str:
        """Extract text from tabular data files."""
        if file_path.suffix.lower() == '.csv':
            df = pd.read_csv(file_path)
        else:  # .xlsx or .xls
            df = pd.read_excel(file_path)
        return df.to_string()

    @staticmethod
    def load_directory(directory: Union[str, Path], 
                      extensions: Optional[List[str]] = None) -> Dict[str, str]:
        """
        Load all files from a directory with specified extensions.
        
        Args:
            directory: Path to the directory
            extensions: List of file extensions to include (e.g., ['.txt', '.pdf'])
            
        Returns:
            Dictionary mapping file paths to their content
        """
        directory = Path(directory)
        if not directory.is_dir():
            logging.error(f"Directory not found: {directory}")
            return {}
            
        extensions = [ext.lower() for ext in (extensions or ['.txt', '.pdf', '.docx', '.csv', '.xlsx'])]
        files = [f for f in directory.glob('*') if f.suffix.lower() in extensions and f.is_file()]
        
        results = {}
        for file_path in files:
            content = DocumentLoader.load_file(file_path)
            if content:
                results[str(file_path)] = content
                
        return results