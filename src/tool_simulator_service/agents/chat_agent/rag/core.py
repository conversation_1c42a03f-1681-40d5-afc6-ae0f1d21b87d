import os
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
import json

from .loader import DocumentLoader
from .spliter import TextSplitter
from .vector_store import VectorStore

class RAGSystem:
    """
    Main RAG (Retrieval-Augmented Generation) system for document processing and retrieval.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the RAG system with configuration.
        
        Args:
            config: Configuration dictionary with the following structure:
                - data_dir: Directory containing documents to process
                - vector_store: Configuration for the vector store
                    - persist_dir: Directory to store the vector store
                    - embedding_model: Name of the embedding model to use
                - chunking: Configuration for text chunking
                    - strategy: 'simple', 'recursive', or 'markdown'
                    - chunk_size: Size of each chunk in characters
                    - chunk_overlap: Overlap between chunks
        """
        self.config = config or {}
        self.document_loader = DocumentLoader()
        self.vector_store = None
        self._initialize_components()
    
    def _initialize_components(self) -> None:
        """Initialize all RAG components based on configuration."""
        # Initialize vector store
        vector_store_config = self.config.get("vector_store", {})
        self.vector_store = VectorStore(vector_store_config)
        
        # Try to load existing vector store if it exists
        if not self.vector_store.load():
            logging.info("No existing vector store found. A new one will be created.")
    
    def process_documents(self, file_paths: Optional[List[Union[str, Path]]] = None) -> None:
        """
        Process documents and add them to the vector store.
        
        Args:
            file_paths: Optional list of file paths to process. If not provided,
                       processes all files in the configured data directory.
        """
        if file_paths is None:
            # Process all files in the data directory
            data_dir = Path(self.config.get("data_dir", "data"))
            if not data_dir.exists():
                logging.error(f"Data directory not found: {data_dir}")
                return
                
            documents = self.document_loader.load_directory(data_dir)
        else:
            # Process specified files
            documents = {}
            for file_path in file_paths:
                content = self.document_loader.load_file(file_path)
                if content is not None:
                    documents[str(file_path)] = content
        
        if not documents:
            logging.warning("No documents found to process.")
            return
        
        # Process and chunk documents
        chunked_docs = self._chunk_documents(documents)
        
        # Add to vector store
        self.vector_store.add_documents(chunked_docs)
        self.vector_store.save()
        
        logging.info(f"Processed {len(documents)} documents into {len(chunked_docs)} chunks.")
    
    def _chunk_documents(self, documents: Dict[str, str]) -> List[Dict[str, Any]]:
        """
        Split documents into chunks based on configuration.
        
        Args:
            documents: Dictionary mapping file paths to their content
            
        Returns:
            List of chunked documents with metadata
        """
        chunking_config = self.config.get("chunking", {})
        splitter = TextSplitter(chunking_config)
        
        chunked_docs = []
        
        for file_path, content in documents.items():
            # Split document into chunks
            chunks = splitter.split_text(content)
            
            # Create document chunks with metadata
            for i, chunk in enumerate(chunks):
                chunked_docs.append({
                    "page_content": chunk,
                    "metadata": {
                        "source": file_path,
                        "chunk_index": i,
                        "total_chunks": len(chunks)
                    }
                })
        
        return chunked_docs
    
    def query(self, query: str, k: int = 4, **kwargs) -> List[Dict[str, Any]]:
        """
        Query the vector store for similar documents.
        
        Args:
            query: The search query
            k: Number of results to return
            **kwargs: Additional arguments for the search
            
        Returns:
            List of matching documents with metadata and scores
        """
        if self.vector_store is None:
            logging.error("Vector store not initialized")
            return []
            
        return self.vector_store.similarity_search(query, k=k, **kwargs)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the RAG system.
        
        Returns:
            Dictionary with RAG system statistics
        """
        if self.vector_store is None:
            return {"status": "not_initialized"}
            
        return {
            "status": "ready",
            "vector_store": self.vector_store.get_stats(),
            "config": self.config
        }
    
    def clear(self) -> None:
        """Clear all documents from the vector store."""
        if self.vector_store is not None:
            self.vector_store.clear()
            self.vector_store.save()


def create_rag_system(config_path: Optional[str] = None) -> RAGSystem:
    """
    Create a RAG system with configuration from a file.
    
    Args:
        config_path: Path to the configuration file. If not provided,
                    looks for 'rag_config.yaml' in the current directory.
                    
    Returns:
        Initialized RAGSystem instance
    """
    import yaml
    from pathlib import Path
    
    if config_path is None:
        config_path = Path(__file__).parent / "rag_config.yaml"
    else:
        config_path = Path(config_path)
    
    if not config_path.exists():
        logging.warning(f"Config file not found at {config_path}. Using default configuration.")
        return RAGSystem()
    
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        return RAGSystem(config)
    except Exception as e:
        logging.error(f"Failed to load config from {config_path}: {str(e)}")
        return RAGSystem()