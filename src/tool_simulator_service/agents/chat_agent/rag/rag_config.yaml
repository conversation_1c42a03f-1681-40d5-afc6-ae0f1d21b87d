# RAG System Configuration

# Directory containing documents to process
data_dir: "data"

# Vector store configuration
vector_store:
  persist_dir: "vector_store"
  embedding_model: "text-embedding-3-small"
  embedding_kwargs:
    model: "text-embedding-3-small"
    openai_api_key: "${OPENAI_API_KEY}"  # Read from environment variable

# Text chunking configuration
chunking:
  strategy: "recursive"  # Options: simple, recursive, markdown
  chunk_size: 1000
  chunk_overlap: 200
  
# Logging configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
