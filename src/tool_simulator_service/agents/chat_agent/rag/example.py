import os
import sys
import logging
from pathlib import Path
from dotenv import load_dotenv

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent.parent.parent.parent
sys.path.append(str(project_root))

# Load environment variables from .env file if it exists
dotenv_path = project_root / '.env'
if dotenv_path.exists():
    load_dotenv(dotenv_path)

# Verify OPENAI_API_KEY is set
if not os.getenv('OPENAI_API_KEY'):
    raise ValueError("OPENAI_API_KEY environment variable is not set. Please set it or create a .env file.")

# Import after setting up the path
from src.tool_simulator_service.agents.chat_agent.config.agent_settings import get_rag_config
from src.tool_simulator_service.agents.chat_agent.rag.core import RAGSystem

def setup_logging(config):
    """Configure logging based on the RAG config."""
    log_config = config.get('logging', {})
    logging.basicConfig(
        level=log_config.get('level', 'INFO'),
        format=log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    )

def main():
    # Get RAG configuration from agent settings
    rag_config = get_rag_config()
    
    # Set up logging
    setup_logging(rag_config)
    logger = logging.getLogger(__name__)
    
    try:
        # Initialize the RAG system with the config
        logger.info("Initializing RAG system...")
        rag = RAGSystem(rag_config)
        
        # Process documents
        logger.info("Processing documents...")
        rag.process_documents()
        
        # Example query
        query = "how do i fix the temprature rise of the pump??"
        logger.info(f"Querying: {query}")
        results = rag.query(query)
        
        # Print results
        print("\nSearch Results:")
        print("-" * 80)
        for i, result in enumerate(results, 1):
            print(f"Result {i} (Score: {result['score']:.4f}):")
            print(f"Source: {result['metadata'].get('source', 'Unknown')}")
            print(f"Content: {result['page_content']}")
            print("-" * 80)
            
        # Print system stats
        stats = rag.get_stats()
        print("\nRAG System Stats:")
        print(f"Status: {stats['status']}")
        print(f"Documents processed: {stats['vector_store'].get('document_count', 0)}")
        
    except Exception as e:
        logger.error(f"An error occurred: {str(e)}", exc_info=True)
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())