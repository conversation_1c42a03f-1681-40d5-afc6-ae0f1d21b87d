from typing import List, Dict, Any, Optional
from langchain_text_splitters import RecursiveCharacterTextSplitter

class TextSplitter:
    """
    A wrapper around <PERSON><PERSON><PERSON><PERSON>'s RecursiveCharacterTextSplitter for text chunking.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the text splitter with configuration.
        
        Args:
            config: Dictionary containing chunking configuration with keys:
                - chunk_size: Target size of each chunk (default: 1000)
                - chunk_overlap: Overlap between chunks (default: 200)
                - separators: List of separators to use for splitting (optional)
        """
        config = config or {}
        chunk_size = int(config.get("chunk_size", 1000))
        chunk_overlap = int(config.get("chunk_overlap", 200))
        
        # Default separators (same as <PERSON><PERSON><PERSON><PERSON>'s default)
        separators = [
            "\n\n",
            "\n",
            " ",
            ". ",
            "!",
            "?",
            ", ",
            ";",
            ":",
            "\"",
            "'",
            "\u201d", 
            "\u2019",
            "]",
            ")",
            "}",
            ">",
            " ",
            "",
        ]
        
        # Use custom separators if provided
        if "separators" in config and config["separators"]:
            separators = config["separators"]
        
        self.splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            separators=separators,
            length_function=len,
            is_separator_regex=False,
        )
    
    def split_text(self, text: str) -> List[str]:
        """
        Split text into chunks.
        
        Args:
            text: Input text to split
            
        Returns:
            List of text chunks
        """
        if not text.strip():
            return []
            
        return self.splitter.split_text(text)