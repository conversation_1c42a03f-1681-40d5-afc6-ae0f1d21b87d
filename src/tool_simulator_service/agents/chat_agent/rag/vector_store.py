import os
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import numpy as np

# Try to import vector store dependencies
try:
    from langchain_community.vectorstores import FAISS
    from langchain_openai import OpenAIEmbeddings
    from langchain_core.documents import Document as LangchainDocument
    from langchain_core.embeddings import Embeddings
    HAS_DEPS = True
except ImportError:
    HAS_DEPS = False
    logging.warning("LangChain dependencies not installed. Install with: pip install langchain-community langchain-core langchain-openai faiss-cpu")

class VectorStore:
    """
    Manages vector storage and retrieval of document chunks.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the vector store.
        
        Args:
            config: Configuration dictionary with keys:
                - persist_dir: Directory to persist the vector store
                - embedding_model: Name of the embedding model to use
                - embedding_kwargs: Additional kwargs for the embedding model
        """
        if not HAS_DEPS:
            raise ImportError("Required dependencies not found. Please install langchain and faiss-cpu")
            
        self.config = config
        self.persist_dir = Path(config.get("persist_dir", "vector_store"))
        self.embedding_model = self._init_embedding_model()
        self.vector_store = None
        self._ensure_persist_dir()
    
    def _ensure_persist_dir(self) -> None:
        """Ensure the persistence directory exists."""
        self.persist_dir.mkdir(parents=True, exist_ok=True)
    
    def _init_embedding_model(self) -> Embeddings:
        """Initialize the embedding model."""
        model_name = self.config.get("embedding_model", "text-embedding-3-small")
        model_kwargs = self.config.get("embedding_kwargs", {}).copy()  # Create a copy to avoid modifying the original
        
        # Remove model from model_kwargs to avoid duplicate parameter
        model_kwargs.pop('model', None)
        
        try:
            return OpenAIEmbeddings(
                model=model_name,
                **model_kwargs
            )
        except Exception as e:
            logging.error(f"Failed to initialize embedding model: {str(e)}")
            raise
    
    def add_documents(self, documents: List[Dict[str, Any]], **kwargs) -> None:
        """
        Add documents to the vector store.
        
        Args:
            documents: List of document dictionaries with 'page_content' and 'metadata' keys
            **kwargs: Additional arguments for the vector store
        """
        if not documents:
            return
            
        # Convert to LangChain Document format
        docs = [
            LangchainDocument(
                page_content=doc.get("page_content", ""),
                metadata=doc.get("metadata", {})
            )
            for doc in documents
        ]
        
        if self.vector_store is None:
            # Create new vector store
            self.vector_store = FAISS.from_documents(
                documents=docs,
                embedding=self.embedding_model,
                **kwargs
            )
        else:
            # Add to existing vector store
            self.vector_store.add_documents(docs, **kwargs)
    
    def similarity_search(
        self, 
        query: str, 
        k: int = 4, 
        filter: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Search for similar documents to the query.
        
        Args:
            query: The search query
            k: Number of results to return
            filter: Optional filter to apply to the search
            **kwargs: Additional arguments for the search
            
        Returns:
            List of matching documents with scores
        """
        if self.vector_store is None:
            return []
            
        # Perform similarity search
        docs_and_scores = self.vector_store.similarity_search_with_score(
            query=query,
            k=k,
            filter=filter,
            **kwargs
        )
        
        # Format results
        results = []
        for doc, score in docs_and_scores:
            result = {
                "page_content": doc.page_content,
                "metadata": doc.metadata,
                "score": float(score)  # Convert numpy float to Python float
            }
            results.append(result)
            
        return results
    
    def save(self) -> None:
        """Save the vector store to disk."""
        if self.vector_store is not None:
            self.vector_store.save_local(self.persist_dir)
    
    def load(self) -> bool:
        """
        Load the vector store from disk if it exists.
        
        Returns:
            bool: True if loaded successfully, False otherwise
        """
        index_path = self.persist_dir / "index.faiss"
        if not index_path.exists():
            return False
            
        try:
            self.vector_store = FAISS.load_local(
                folder_path=str(self.persist_dir),
                embeddings=self.embedding_model,
                allow_dangerous_deserialization=True  # Required for FAISS
            )
            return True
        except Exception as e:
            logging.error(f"Failed to load vector store: {str(e)}")
            return False
    
    def exists(self) -> bool:
        """Check if the vector store exists on disk."""
        index_path = self.persist_dir / "index.faiss"
        return index_path.exists()
    
    def get_stats(self) -> Dict[str, int]:
        """
        Get statistics about the vector store.
        
        Returns:
            Dictionary with vector store statistics
        """
        if self.vector_store is None:
            return {"document_count": 0, "dimension": 0}
        
        # Get document count
        doc_count = len(self.vector_store.docstore._dict)
        
        # Get embedding dimension by embedding a test string
        dimension = 0
        if doc_count > 0:
            try:
                # Use a small test string to get the embedding dimension
                test_embedding = self.embedding_model.embed_query("test")
                dimension = len(test_embedding)
            except Exception as e:
                logging.warning(f"Could not determine embedding dimension: {e}")
            
        return {
            "document_count": doc_count,
            "dimension": dimension
        }
    
    def clear(self) -> None:
        """Clear all documents from the vector store."""
        if self.vector_store is not None:
            # Create a new empty vector store with the same configuration
            self.vector_store = FAISS.from_texts(
                texts=[""],
                embedding=self.embedding_model
            )
            self.save()
