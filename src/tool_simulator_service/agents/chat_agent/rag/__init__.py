"""
RAG (Retrieval-Augmented Generation) module for document processing and retrieval.

This module provides functionality to load, process, and retrieve documents using
vector embeddings and similarity search.
"""

from .core import RAGSystem, create_rag_system
from .loader import DocumentLoader
from .spliter import TextSplitter
from .vector_store import VectorStore

__all__ = [
    'RAGSystem',
    'create_rag_system',
    'DocumentLoader',
    'TextSplitter',
    'VectorStore'
]