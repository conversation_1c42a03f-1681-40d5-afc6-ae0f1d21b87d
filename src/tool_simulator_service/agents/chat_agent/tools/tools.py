from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any, Optional, Generator
import logging
import json
from pathlib import Path

from langchain_core.tools import tool
from pydantic import BaseModel, Field
import json
from functools import lru_cache
from typing import Dict, Optional, Generator, Any
import logging

# Set up logging
logger = logging.getLogger(__name__)

# In-memory cache for incidents
_incident_cache: Dict[str, Dict[str, Any]] = {}
_incident_file_path = "/home/<USER>/Downloads/telemetry-engine/src/tool_simulator_service/agents/incident_agent/logs/incident.jsonl"

def _load_incidents() -> None:
    """Load all incidents into memory if not already loaded."""
    if _incident_cache:
        return
        
    try:
        with open(_incident_file_path, 'r') as file:
            for line in file:
                try:
                    incident = json.loads(line)
                    if 'id' in incident:
                        _incident_cache[incident['id']] = incident
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse line: {line.strip()}. Error: {e}")
                    continue
    except FileNotFoundError:
        logger.error(f"Incident file not found: {_incident_file_path}")
        raise

def _stream_incidents() -> Generator[Dict[str, Any], None, None]:
    """Stream incidents one at a time for memory efficiency."""
    try:
        with open(_incident_file_path, 'r') as file:
            for line in file:
                try:
                    yield json.loads(line)
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse line: {line.strip()}. Error: {e}")
                    continue
    except FileNotFoundError:
        logger.error(f"Incident file not found: {_incident_file_path}")
        raise

@lru_cache(maxsize=128)
def get_incident_report(incident_id: str) -> Optional[Dict[str, Any]]:
    """
    Retrieve a specific incident by its ID with caching.
    
    Args:
        incident_id: The unique identifier of the incident to retrieve
        
    Returns:
        dict: The incident data if found, None otherwise
    """
    # First try the in-memory cache
    if _incident_cache:
        return _incident_cache.get(incident_id)
    
    # If not in cache, try to load all incidents
    try:
        _load_incidents()
        return _incident_cache.get(incident_id)
    except FileNotFoundError:
        # If loading all fails, try streaming search as fallback
        try:
            for incident in _stream_incidents():
                if incident.get('id') == incident_id:
                    return incident
        except FileNotFoundError:
            pass
    
    return None

# Try to import RAG dependencies
try:
    from src.tool_simulator_service.agents.chat_agent.rag.core import RAGSystem
    from src.tool_simulator_service.agents.chat_agent.config.agent_settings import (
        get_rag_config, 
        get_event_logs_config
    )
    HAS_RAG = True
except ImportError as e:
    logger.warning(f"RAG dependencies not available. Document search will not work. Error: {e}")
    HAS_RAG = False

# Initialize RAG system if available
rag_system = None
if HAS_RAG:
    try:
        rag_config = get_rag_config()
        rag_system = RAGSystem(rag_config)
    except Exception as e:
        logger.error(f"Failed to initialize RAG system: {e}")
        rag_system = None

# Load event logs configuration
event_logs_config = {}
if HAS_RAG:
    try:
        event_logs_config = get_event_logs_config()
        # Convert relative path to absolute path relative to project root
        log_file = Path(event_logs_config.get('log_file', ''))
        if not log_file.is_absolute():
            # Assuming the config is in src/tool_simulator_service/agents/chat_agent/config
            base_dir = Path(__file__).parent.parent.parent.parent.parent.parent
            log_file = (base_dir / log_file).resolve()
        event_logs_config['log_file'] = str(log_file)
        logger.info(f"Event logs will be loaded from: {log_file}")
    except Exception as e:
        logger.error(f"Failed to load event logs config: {e}")
        event_logs_config = {}

class EventLogs:
    """Helper class to handle event logs loading and querying."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.log_file = config.get('log_file')
        self.field_names = config.get('field_names', {})
        self._log_entries = None
    
    def _load_logs(self) -> Generator[Dict[str, Any], None, None]:
        """Lazily load log entries from the JSONL file."""
        if not self.log_file or not Path(self.log_file).exists():
            logger.error(f"Log file not found: {self.log_file}")
            return
            
        try:
            with open(self.log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        yield json.loads(line.strip())
                    except json.JSONDecodeError as e:
                        logger.warning(f"Failed to parse log line: {e}")
                        continue
        except Exception as e:
            logger.error(f"Error reading log file {self.log_file}: {e}")
    
    def search_events(
        self, 
        target_time: datetime, 
        window_seconds: int = 30, 
        max_results: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Search for events within a time window around the target time.
        
        Args:
            target_time: The center time to search around
            window_seconds: Seconds before and after target_time to search
            max_results: Maximum number of results to return
            
        Returns:
            List of matching log entries with metadata
        """
        if not self.log_file or not Path(self.log_file).exists():
            logger.warning(f"Log file not found: {self.log_file}")
            return []

        # Ensure target_time is timezone-aware
        if target_time.tzinfo is None:
            target_time = target_time.replace(tzinfo=timezone.utc)
            
        start_time = target_time - timedelta(seconds=window_seconds)
        end_time = target_time + timedelta(seconds=window_seconds)
        
        results = []
        timestamp_field = self.field_names.get('timestamp', 'timestamp')
        
        for entry in self._load_logs():
            try:
                # Parse timestamp from the entry
                entry_time_str = entry.get(timestamp_field, '')
                if not entry_time_str:
                    continue
                    
                # Handle different timestamp formats
                try:
                    # Handle ISO format with timezone offset without colon (e.g., +0530)
                    if '+' in entry_time_str and ':' not in entry_time_str.split('+')[1]:
                        # Insert colon in timezone offset (e.g., +0530 -> +05:30)
                        tz_part = entry_time_str.split('+')[1]
                        if len(tz_part) >= 3:  # Ensure we have at least hours and minutes
                            tz_part = f"{tz_part[:2]}:{tz_part[2:]}"
                            entry_time_str = entry_time_str.split('+')[0] + '+' + tz_part
                    
                    # Handle Zulu time
                    if entry_time_str.endswith('Z'):
                        entry_time = datetime.fromisoformat(entry_time_str.replace('Z', '+00:00'))
                    else:
                        entry_time = datetime.fromisoformat(entry_time_str)
                        
                except ValueError as e:
                    # Try other possible formats if needed
                    try:
                        entry_time = datetime.strptime(entry_time_str, '%Y-%m-%dT%H:%M:%S%z')
                    except ValueError:
                        logger.warning(f"Could not parse timestamp: {entry_time_str} - {e}")
                        continue
                
                # Ensure entry_time is timezone-aware
                if entry_time.tzinfo is None:
                    entry_time = entry_time.replace(tzinfo=timezone.utc)
                
                # Check if entry is within the time window
                if start_time <= entry_time <= end_time:
                    results.append({
                        'timestamp': entry_time.isoformat(),
                        'level': entry.get(self.field_names.get('level', 'level'), 'INFO'),
                        'message': entry.get(self.field_names.get('message', 'message'), ''),
                        'source': entry.get(self.field_names.get('source', 'source'), 'unknown'),
                        'raw': entry  # Include the raw entry for reference
                    })
                    
                    # Early exit if we've reached max results
                    if len(results) >= max_results:
                        break
                        
            except Exception as e:
                logger.error(f"Error processing log entry: {e}")
                continue
        
        # Sort by timestamp
        results.sort(key=lambda x: x['timestamp'])
        return results

# Initialize event logs handler
event_logs = EventLogs(event_logs_config) if event_logs_config else None

class DocumentSearchInput(BaseModel):
    """Input for document search tool."""
    query: str = Field(..., description="Search query to find relevant documents")
    k: int = Field(4, description="Number of results to return")

@tool(args_schema=DocumentSearchInput)
def search_documents(query: str, k: int = 4) -> List[Dict[str, Any]]:
    """
    Search through documentation using semantic search.
    
    Args:
        query: The search query to find relevant documents
        k: Number of results to return (default: 4)
        
    Returns:
        List of relevant document chunks with metadata
    """
    if not HAS_RAG or rag_system is None:
        return [{"error": "Document search is not available. RAG system not initialized."}]
    
    try:
        results = rag_system.query(query, k=k)
        
        # Format results for better readability
        formatted_results = []
        for i, doc in enumerate(results, 1):
            formatted_results.append({
                "rank": i,
                "content": doc.get("page_content", "").strip(),
                "source": doc.get("metadata", {}).get("source", "unknown"),
                "score": doc.get("score", 0.0)
            })
            
        return formatted_results
    except Exception as e:
        logger.error(f"Error in document search: {e}")
        return [{"error": f"Error performing search: {str(e)}"}]

class EventSearchInput(BaseModel):
    """Input for event search tool."""
    timestamp: str = Field(..., description="ISO format timestamp to search around (e.g., 2023-01-01T12:00:00)")
    window_seconds: int = Field(30, description="Time window in seconds to search before and after the timestamp")
    max_results: int = Field(10, description="Maximum number of results to return")

@tool(args_schema=EventSearchInput)
def search_events(
    timestamp: str, 
    window_seconds: Optional[int] = None, 
    max_results: Optional[int] = None
) -> List[Dict[str, Any]]:
    """
    Search for log events within a time window around the specified timestamp.
    
    Args:
        timestamp: ISO format timestamp to search around (e.g., 2023-01-01T12:00:00 or 2023-01-01T12:00:00+05:30)
        window_seconds: Time window in seconds to search before and after the timestamp. 
                      If not provided, uses default from config.
        max_results: Maximum number of results to return. If not provided, uses default from config.
        
    Returns:
        List of log events within the specified time window, each containing:
        - timestamp: ISO format timestamp of the event
        - level: Log level (INFO, WARNING, ERROR, etc.)
        - message: Log message
        - source: Source of the log entry
        - raw: Complete raw log entry
    """
    if not event_logs:
        return [{"error": "Event logs system is not properly configured"}]
    
    try:
        # Use provided values or fall back to config defaults
        window = window_seconds or event_logs.config.get('default_window_seconds', 30)
        limit = max_results or event_logs.config.get('default_max_results', 10)
        
        # Parse the input timestamp
        try:
            target_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        except ValueError as e:
            return [{"error": f"Invalid timestamp format: {e}. Please use ISO 8601 format (e.g., 2023-01-01T12:00:00+05:30)"}]
        
        # Search for events
        events = event_logs.search_events(
            target_time=target_time,
            window_seconds=window,
            max_results=limit
        )
        
        if not events:
            return [{"message": f"No events found within ±{window} seconds of {timestamp}"}]
            
        return events
        
    except Exception as e:
        error_msg = f"Error searching events: {str(e)}"
        logger.exception(error_msg)
        return [{"error": error_msg}]

# Export the tools for the agent to use
#TODO:remove the hardcoded paths and move to the config 
LOG_FILE_PATH = Path("src/tool_simulator_service/agents/incident_agent/logs/events.jsonl")


class GetRelevantLogsArgs(BaseModel):
    pivot_event_json_str: str = Field(description="The JSON string of the pivot event that triggered the analysis. Must contain a 'timestamp' field.")
    window_seconds_before: int = Field(default=30, description="How many seconds before the pivot event's timestamp to include logs.")
    window_seconds_after: int = Field(default=5, description="How many seconds after the pivot event's timestamp to include logs (to catch immediate consequences).")

@tool(args_schema=GetRelevantLogsArgs)
def get_relevant_logs_for_event(
    tool_arg_input: str,
    window_seconds_before: int = 30,
    window_seconds_after: int = 5
) -> str:
    """
    Loads telemetry alert logs from events.jsonl that are relevant to a given pivot event's timestamp.
    It fetches logs within a time window around the pivot event.
    The input (tool_arg_input) is expected to either be the direct pivot_event_json_str,
    or a JSON string representing the entire tool arguments dictionary.
    The tool uses the top-level 'timestamp' field from log entries for window comparison.
    """
    print(f"\n--- TOOL: get_relevant_logs_for_event ---")
    print(f"[DEBUG] Initial raw tool_arg_input (type: {type(tool_arg_input)}): {tool_arg_input!r}")
    print(f"[DEBUG] Initial window_seconds_before: {window_seconds_before}")
    print(f"[DEBUG] Initial window_seconds_after: {window_seconds_after}")

    actual_pivot_event_json_str = None
    
    try:
        potential_tool_args_dict = json.loads(tool_arg_input)
        if isinstance(potential_tool_args_dict, dict) and "pivot_event_json_str" in potential_tool_args_dict:
            print(f"[INFO] tool_arg_input appears to be the full arguments dictionary. Extracting 'pivot_event_json_str'.")
            actual_pivot_event_json_str = potential_tool_args_dict.get("pivot_event_json_str")
        else:
            print(f"[INFO] tool_arg_input parsed to JSON but not the args dict. Assuming it's the pivot_event_json_str itself.")
            actual_pivot_event_json_str = tool_arg_input
    except json.JSONDecodeError:
        print(f"[INFO] tool_arg_input was not valid JSON. Assuming it IS the pivot_event_json_str itself (this might be problematic).")
        actual_pivot_event_json_str = tool_arg_input
    
    if not isinstance(actual_pivot_event_json_str, str):
        error_msg = f"[ERROR] Could not resolve actual_pivot_event_json_str to a string. Type: {type(actual_pivot_event_json_str)}, Value: {actual_pivot_event_json_str!r}"
        print(error_msg)
        return error_msg

    print(f"[DEBUG] Effective pivot_event_json_str to be parsed: {actual_pivot_event_json_str!r}")
    print(f"[DEBUG] Effective window_seconds_before: {window_seconds_before}")
    print(f"[DEBUG] Effective window_seconds_after: {window_seconds_after}")

    relevant_log_entries = []
    pivot_event_data = None

    try:
        pivot_event_data = json.loads(actual_pivot_event_json_str)
    except json.JSONDecodeError as e:
        print(f"[ERROR] Parsing the effective pivot_event_json_str failed: {e}. String was: {actual_pivot_event_json_str!r}")
        return "Error: Invalid JSON string provided for the pivot event."
    except TypeError as e:
        print(f"[ERROR] TypeError while parsing effective pivot_event_json_str (was it None or not a string?): {e}. Value: {actual_pivot_event_json_str!r}")
        return "Error: Pivot event data was not a valid string for JSON parsing."


    if not isinstance(pivot_event_data, dict):
        print(f"[ERROR] Parsed pivot_event_data is not a dictionary. Type: {type(pivot_event_data)}")
        return "Error: Parsed pivot event data did not result in a dictionary."

    pivot_input_timestamp_str = pivot_event_data.get("timestamp")
    print(f"[DEBUG] Value of 'timestamp' field from PIVOT event data: {pivot_input_timestamp_str!r}")

    if not pivot_input_timestamp_str:
        print(f"[ERROR] 'timestamp' key not found in PIVOT event data or its value is empty/None. Keys: {list(pivot_event_data.keys())}")
        return "Error: Pivot event JSON does not contain a 'timestamp' field or it's empty."

    try:
        pivot_dt = datetime.fromisoformat(pivot_input_timestamp_str)
        if pivot_dt.tzinfo is None:
            print(f"[WARNING] Pivot timestamp '{pivot_input_timestamp_str}' was naive. Assuming UTC.")
            pivot_dt = pivot_dt.replace(tzinfo=timezone.utc)
    except ValueError as e:
        print(f"[ERROR] Parsing pivot timestamp string '{pivot_input_timestamp_str}' failed: {e}")
        return f"Error: Invalid ISO 8601 format for pivot timestamp: '{pivot_input_timestamp_str}'."
    except TypeError as e:
        print(f"[ERROR] TypeError parsing pivot timestamp (was None or not string?): '{pivot_input_timestamp_str}'. Error: {e}")
        return "Error: Timestamp field from pivot event was invalid for parsing."


    window_start_dt = pivot_dt - timedelta(seconds=window_seconds_before)
    window_end_dt = pivot_dt + timedelta(seconds=window_seconds_after)
    print(f"[INFO] Calculated time window: Start={window_start_dt.isoformat()}, End={window_end_dt.isoformat()}")

    if not LOG_FILE_PATH.exists():
        print(f"[ERROR] Log file not found at {LOG_FILE_PATH}")
        return "Error: Log file not found."

    print(f"[INFO] Reading log file: {LOG_FILE_PATH}")
    processed_lines = 0
    found_relevant_count = 0
    with open(LOG_FILE_PATH, 'r') as f:
        for line_number, line in enumerate(f, 1):
            processed_lines += 1  
            try:
                log_entry = json.loads(line)
            except json.JSONDecodeError:
                continue

            log_file_entry_ts_str = log_entry.get("timestamp")

            if not log_file_entry_ts_str:
                continue

            try:
                log_dt = datetime.fromisoformat(log_file_entry_ts_str)
                if log_dt.tzinfo is None:
                    log_dt = log_dt.replace(tzinfo=timezone.utc)
            except ValueError:
                continue

            if window_start_dt <= log_dt <= window_end_dt:
                relevant_log_entries.append(line.strip())
                found_relevant_count +=1

    print(f"[INFO] Processed {processed_lines} lines from log file.")
    if not relevant_log_entries:
        print(f"[INFO] No relevant log entries found for pivot {pivot_dt.isoformat()} in window.")
        return f"No relevant log entries found for pivot {pivot_dt.isoformat()} within the time window."

    print(f"[INFO] Found {found_relevant_count} relevant entries.")
    return "\n".join(relevant_log_entries)

TOOLS = [search_documents, get_relevant_logs_for_event]
