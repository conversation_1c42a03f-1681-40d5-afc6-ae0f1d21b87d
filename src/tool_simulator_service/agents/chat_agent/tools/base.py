from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel

class BaseTool(ABC):
    """
    Abstract base class for all tools.
    """
    def __init__(self, tool_id: str, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the base tool.
        
        Args:
            tool_id: Unique identifier for this tool
            config: Optional configuration dictionary for this tool
        """
        self.tool_id = tool_id
        self.config = config or {}

    @abstractmethod
    async def execute(self, inputs: Dict[str, Any], options: Optional[Dict[str, Any]] = None) -> Any:
        """
        Execute the tool with the given inputs and options.
        
        Args:
            inputs: Input parameters for the tool
            options: Optional execution options
            
        Returns:
            Any: The result of the tool execution
            
        Raises:
            ToolError: If the tool execution fails
        """
        pass