# src/tool_simulator_service/agents/chat_agent/callbacks.py

import asyncio
import json
from typing import Any, Dict

from langchain.callbacks.base import Base<PERSON>allback<PERSON>and<PERSON>
from sse_starlette.sse import ServerSentEvent

class Threaded<PERSON><PERSON>backHandler(BaseCallbackHandler):
    def __init__(self, queue: asyncio.Queue, loop: asyncio.AbstractEventLoop):
        self.queue = queue
        self.loop = loop

    def on_agent_action(self, action, **kwargs: Any) -> Any:
        tool_name = action.tool
        message = json.dumps({"type": "status", "message": f"Thinking... (using tool: {tool_name})"})
        event = ServerSentEvent(data=message, event="status")
        self.loop.call_soon_threadsafe(self.queue.put_nowait, event)

    def on_chain_end(self, outputs: Dict[str, Any], **kwargs: Any) -> None:
        # ---- THIS METHOD NOW DOES NOTHING ----
        # The stream will be closed by the main task wrapper.
        pass

    def on_chain_error(self, error: Exception | KeyboardInterrupt, **kwargs: Any) -> None:
        message = json.dumps({"type": "error", "content": f"An error occurred: {error}"})
        event = ServerSentEvent(data=error_message, event="error")
        self.loop.call_soon_threadsafe(self.queue.put_nowait, event)
        # We also remove the None from here. The finally block will handle it.