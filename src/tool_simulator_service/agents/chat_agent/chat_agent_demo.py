import json
from typing import List, Literal
from langchain_openai import ChatOpenAI
from langchain_core.tools import tool
from langchain_core.prompts import PromptTemplate
from langchain.agents import AgentExecutor, create_react_agent
from src.tool_simulator_service.config.settings import settings
from src.tool_simulator_service.agents.chat_agent.tools.tools import search_documents,get_relevant_logs_for_event
import logging
#TODO:Demo script only the main module is pending 


tools = [get_relevant_logs_for_event,search_documents]
prompt_template = """You are a manufacturing AI assistant. Your goal is to provide focused answers based on the user's request. You MUST follow all instructions precisely.

You have access to the following tools to gather information:

{tools}
{tool_names}
**CRITICAL INSTRUCTION FOR USING TOOLS:**
- When you need to understand what happened during an incident, use the `get_relevant_logs_for_event` tool.
- For this tool, you MUST pass the **entire `Incident Context` JSON** from the input as a single string to the `pivot_event_json_str` parameter.
- **DO NOT** invent your own input format like `'{{"event_id": ...}}'`. This will fail.
- **DO NOT** pass only a part of the context. You must pass the whole thing.

**Output Modes & Required Actions:**
You will be given an 'Output Mode'. You must follow the workflow for that mode:
- If `Output Mode` is `root_cause`:
  1. Use `get_relevant_logs_for_event`.
  2. Analyze the logs and provide a `Final Answer` summarizing ONLY the root cause. Do not use other tools.
- If `Output Mode` is `resolution`:
  1. Use `get_relevant_logs_for_event` to get context.
  2. Use `search_documents` to find the SOP for the observed anomaly.
  3. Provide a `Final Answer` with ONLY the actionable resolution steps.
- If `Output Mode` is `full`:
  1. Use `get_relevant_logs_for_event`.
  2. Use `search_documents`.
  3. Provide a `Final Answer` with both the root cause and the resolution steps.

**YOUR RESPONSE FORMAT MUST BE:**
Question: [The user's full input, including the Output Mode and Incident Context]
Thought: [Your step-by-step reasoning about your goal based on the Output Mode, and which tool you need to use next based on its description above.]
Action: [The single tool to use, which must be one of {tool_names}]
Action Input: [The correctly formatted input for the chosen tool]
Observation: [The result returned by the tool]
... (this Thought/Action/Observation cycle can repeat)
Thought: I have all the information required for the requested Output Mode. I will now provide the final answer.
Final Answer: [Your final, focused answer. Do not add extra commentary. explain your findings clear and conciesly]

Begin!

Question: {input}
{agent_scratchpad}
"""

prompt = PromptTemplate.from_template(prompt_template)
prompt = prompt.partial(tools=tools, tool_names=", ".join([t.name for t in tools]))

# --- Agent Setup ---
llm = ChatOpenAI(temperature=0, model="gpt-4o", api_key=settings.api.agent.OPENAI_API_KEY)
agent_runnable = create_react_agent(llm=llm, tools=tools, prompt=prompt)
_agent_executor = AgentExecutor(agent=agent_runnable, tools=tools, verbose=True, handle_parsing_errors=True, max_iterations=7)

# --- Agent Runner (UPDATED with output_mode) ---
def run_chat_agent(
    user_query: str,
    incident_context: dict,
    output_mode: Literal["full", "root_cause", "resolution"] = "full",
    callbacks: List[object] = None,
):
    """
    Runs the chat agent with a specific output mode.

    Args:
        user_query: The user's question.
        incident_context: The incident data.
        output_mode: Controls the type of answer ("full", "root_cause", or "resolution").
    """
    logging.info(f"\n--- Running Agent with Output Mode: '{output_mode.upper()}' ---")
    context_str = json.dumps(incident_context)
    # The Output Mode is now part of the input, guiding the agent's behavior
    prompt_input = (
        f"Output Mode: {output_mode}\n\n"
        f"Incident Context: {context_str}\n\n"
        f"User Query: {user_query}"
    )
    response = _agent_executor.invoke(
        {"input": prompt_input},
        config={"callbacks": callbacks} if callbacks else None
    )
    return response

# --- CLI Runner (UPDATED to show all modes) ---
if __name__ == "__main__":
    # Create dummy log file

    sample_incident_context={"timestamp": "2025-06-18T16:11:41+0530", "level": "INFO", "logger_name": "incident_logger", "id": "a547e716-02f4-434f-92e6-9e2e3cb288a0", "display_id": "INC-002", "title": "Significant OEE Drop Due to Temperature Anomalies and Machine Uptime Reduction", "status": "in progress", "severity": "high", "assigned_team": "Maintenance Team", "reported_at": "2025-06-18T10:41:27Z", "description": "An OEE drop from 88.06 to 72.11 was observed, coinciding with a series of temperature anomalies in temperature_zone_temperature_zoneB. These anomalies showed a significant increase in temperature, potentially affecting machine performance. Additionally, a machine uptime reduction from 91.67 to 75.0 was recorded at the same time, indicating a possible correlation between the temperature anomalies and machine performance issues.", "updates": ["Log analysis initiated based on pivot event.", "Relevant logs from window 2025-06-18T10:41:11 to 2025-06-18T10:41:27 fetched."], "event_occurrence_timestamp": "2025-06-18T10:41:41.696423+00:00"}

    user_question = "Tell me about this incident."

    try:
        # 1. Get the FULL report (root cause + resolution)
        full_result = run_chat_agent(
            user_query=user_question,
            incident_context=sample_incident_context,
            output_mode="full"
        )
        print("\n" + "="*20 + " ✅ FINAL ANSWER (FULL) " + "="*20)
        print(full_result.get('output'))
        print("="*62 + "\n")

        # 2. Get ROOT CAUSE ONLY
        root_cause_result = run_chat_agent(
            user_query=user_question,
            incident_context=sample_incident_context,
            output_mode="root_cause"
        )
        print("\n" + "="*20 + " ✅ FINAL ANSWER (ROOT CAUSE ONLY) " + "="*20)
        print(root_cause_result.get('output'))
        print("="*62 + "\n")

        # 3. Get RESOLUTION ONLY
        resolution_result = run_chat_agent(
            user_query=user_question,
            incident_context=sample_incident_context,
            output_mode="resolution"
        )
        print("\n" + "="*20 + " ✅ FINAL ANSWER (RESOLUTION ONLY) " + "="*20)
        print(resolution_result.get('output'))
        print("="*62 + "\n")

    except Exception as err:
        logging.error(f"Final failure: {err}", exc_info=True)