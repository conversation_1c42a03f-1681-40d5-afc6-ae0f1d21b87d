from langgraph.prebuilt import create_react_agent

from src.tool_simulator_service.agents.chat_agent.config.prompts import apply_prompt_template
from src.tool_simulator_service.agents.chat_agent.config.agent_settings import get_llm_for_agent


# Create agents using configured LLM types
def create_agent(agent_name: str, agent_type: str, tools: list, prompt_template: str):
    """Factory function to create agents with consistent configuration."""
    return create_react_agent(
        name=agent_name,
        model=get_llm_for_agent(agent_name),
        tools=tools,
        prompt=lambda state: apply_prompt_template(prompt_template, state),
    )
