from langgraph.prebuilt import create_react_agent
from typing import Dict, Any, List

from src.tool_simulator_service.agents.chat_agent.config.prompts import apply_prompt_template
from src.tool_simulator_service.agents.chat_agent.config.agent_settings import get_llm_for_agent


def create_default_chat_agent_state(langgraph_state: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert LangGraph state to ChatAgentState format with sensible defaults.

    LangGraph create_react_agent provides minimal state:
    - messages: List of conversation messages
    - is_last_step: <PERSON><PERSON><PERSON> indicating if this is the final step
    - remaining_steps: Number of steps remaining

    We need to provide defaults for all ChatAgentState fields.
    """
    # Extract user query from the last human message if available
    user_query = "User interaction"
    if "messages" in langgraph_state and langgraph_state["messages"]:
        for msg in reversed(langgraph_state["messages"]):
            if hasattr(msg, 'content') and msg.content:
                user_query = msg.content
                break

    # Create full ChatAgentState structure with defaults
    chat_agent_state = {
        # LangGraph fields
        "messages": langgraph_state.get("messages", []),
        "is_last_step": langgraph_state.get("is_last_step", False),
        "remaining_steps": langgraph_state.get("remaining_steps", 25),

        # ChatAgentState fields with defaults
        "user_query": user_query,
        "incident_context": None,
        "current_plan": None,
        "plan_iterations": 0,
        "root_cause": "",
        "root_cause_feedback": "",
        "rca_tools_used": [],
        "resolution_plan": "",
        "resolution_feedback": "",
        "resolution_steps": None,
        "final_report": "",
        "observations": [],
        "rc_feedback_status": None,
        "res_feedback_status": None,
        "step": "agent_execution",
        "auto_accepted_plan": False,

        # Configuration defaults
        "max_step_num": 5,
        "enable_doc_steps": True,
        "max_plan_iterations": 3,
        "team_timeout": 60
    }

    return chat_agent_state


def create_prompt_adapter(prompt_template: str):
    """Create a prompt function that adapts LangGraph state to ChatAgentState format."""
    def prompt_function(langgraph_state: Dict[str, Any]) -> List[Dict[str, str]]:
        # Convert LangGraph state to ChatAgentState format
        chat_agent_state = create_default_chat_agent_state(langgraph_state)

        # Apply our prompt template
        return apply_prompt_template(prompt_template, chat_agent_state)

    return prompt_function


# Create agents using configured LLM types
def create_agent(agent_name: str, agent_type: str, tools: list, prompt_template: str):
    """Factory function to create agents with consistent configuration."""
    return create_react_agent(
        name=agent_name,
        model=get_llm_for_agent(agent_name),
        tools=tools,
        prompt=create_prompt_adapter(prompt_template),
    )
