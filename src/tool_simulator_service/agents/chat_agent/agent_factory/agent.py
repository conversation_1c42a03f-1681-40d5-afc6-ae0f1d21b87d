from langgraph.prebuilt import create_react_agent
from typing import Dict, Any, List

from src.tool_simulator_service.agents.chat_agent.config.prompts import apply_prompt_template
from src.tool_simulator_service.agents.chat_agent.config.agent_settings import get_llm_for_agent


def create_state_aware_prompt(prompt_template: str):
    """Create a prompt function that adapts LangGraph state to include user_query."""
    def prompt_function(langgraph_state: Dict[str, Any]) -> List[Dict[str, str]]:
        # Extract user query from the most recent human message
        user_query = "User interaction"
        if "messages" in langgraph_state and langgraph_state["messages"]:
            for msg in reversed(langgraph_state["messages"]):
                if hasattr(msg, 'content') and msg.content and hasattr(msg, 'type') and msg.type == 'human':
                    user_query = msg.content
                    break

        # Create enhanced state with required fields for our prompts
        enhanced_state = dict(langgraph_state)
        enhanced_state.update({
            "user_query": user_query,
            "step": "agent_execution",
            "plan_iterations": 0,
            "auto_accepted_plan": False,
            "observations": [],
            "current_plan": None,
            "root_cause": "",
            "rca_tools_used": [],
            "incident_context": None,
            "resolution_plan": "",
            "resolution_feedback": "",
            "resolution_steps": None,
            "root_cause_feedback": "",
            "rc_feedback_status": None,
            "res_feedback_status": None,
            "max_step_num": 5,
            "enable_doc_steps": True
        })

        return apply_prompt_template(prompt_template, enhanced_state)

    return prompt_function


# Create agents using configured LLM types
def create_agent(agent_name: str, agent_type: str, tools: list, prompt_template: str):
    """Factory function to create agents with consistent configuration."""
    return create_react_agent(
        name=agent_name,
        model=get_llm_for_agent(agent_name),
        tools=tools,
        prompt=create_state_aware_prompt(prompt_template),
    )
