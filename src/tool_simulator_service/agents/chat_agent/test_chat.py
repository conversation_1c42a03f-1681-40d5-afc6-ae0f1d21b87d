"""
Interactive chat interface for testing the agent.

Usage:
    python -m src.tool_simulator_service.agents.chat_agent.test_chat
"""
import asyncio
from typing import Dict, Any
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.graph import StateGraph
from src.tool_simulator_service.agents.chat_agent.graph.builder import build_graph
from src.tool_simulator_service.agents.chat_agent.graph.state import ChatAgentState

# Initialize the graph
graph = build_graph()

async def stream_graph_updates(user_input: str, state: Dict[str, Any] = None):
    """Stream updates from the graph for a given user input."""
    print("\n[DEBUG] Starting stream_graph_updates")
    
    try:
        if state is None:
            print("[DEBUG] Initializing new state")
            state = {
                "messages": [HumanMessage(content=user_input)],
                "observations": [],
                "plan_iterations": 0,
                "current_plan": None,
                "plan_approved": False,
                "plan_approval_ts": None,
                "plan_feedback": None,
                "error": None,
                "step": "coordinator",
                "auto_accepted_plan": False,
                "user_query": user_input,
                "incident_context": {
                    "environment": "production",
                    "severity": "medium",
                    "service": "unknown"
                },
                "user_feedback": None,
                "assigned_team": None,
                "current_step": 0,
                "max_steps": 5,
                "execution_results": []
            }
        else:
            print("[DEBUG] Updating existing state")
            # Ensure all required fields exist
            defaults = {
                "messages": [HumanMessage(content=user_input)],
                "observations": [],
                "plan_iterations": 0,
                "error": None,
                "step": "coordinator",
                "auto_accepted_plan": False,
                "user_query": user_input,
                "incident_context": state.get("incident_context", {
                    "environment": "production",
                    "severity": "medium",
                    "service": "unknown"
                }),
                "user_feedback": None,
                "assigned_team": None,
                "current_step": 0,
                "max_steps": 5,
                "execution_results": []
            }
            
            # Update state with defaults for any missing fields
            for key, default in defaults.items():
                if key not in state:
                    state[key] = default
                    
            # Append new message
            if "messages" in state and isinstance(state["messages"], list):
                state["messages"].append(HumanMessage(content=user_input))
            else:
                state["messages"] = [HumanMessage(content=user_input)]
        
        print("[DEBUG] State before processing:", {k: v for k, v in state.items() if k != 'messages'})
        
        # Initialize the state object with the validated state
        try:
            agent_state = ChatAgentState(**state)
            print("[DEBUG] Successfully created ChatAgentState")
        except Exception as e:
            print(f"[ERROR] Failed to create ChatAgentState: {str(e)}")
            print(f"[DEBUG] State keys: {state.keys()}")
            print(f"[DEBUG] State values: {state}")
            raise
        
        # Stream the graph execution
        try:
            print("[DEBUG] Starting graph execution")
            async for event in graph.astream(
                agent_state, 
                stream_mode="values", 
                config={
                    "configurable": {
                        "max_plan_iterations": 3,
                        "max_step_num": 5,
                        "team_timeout": 60,
                        "enable_doc_steps": True
                    }
                }
            ):
                print("[DEBUG] Received event:", event)
                for value in event.values():
                    if not isinstance(value, dict):
                        print(f"[DEBUG] Skipping non-dict value: {value}")
                        continue
                        
                    # Update state with the latest values
                    print("[DEBUG] Updating state with:", {k: v for k, v in value.items() if k != 'messages'})
                    state.update({k: v for k, v in value.items() if v is not None})
                    
                    # Print any new messages
                    if "messages" in value and isinstance(value["messages"], list) and value["messages"]:
                        last_message = value["messages"][-1]
                        if isinstance(last_message, AIMessage):
                            print("\nAssistant:", last_message.content)
                        elif isinstance(last_message, HumanMessage):
                            print("\nYou:", last_message.content)
                        else:
                            print(f"\n[DEBUG] Unhandled message type: {type(last_message).__name__}")
                    
                    # Update messages in state
                    if "messages" in value and isinstance(value["messages"], list):
                        if "messages" not in state:
                            state["messages"] = []
                        for msg in value["messages"]:
                            if msg not in state["messages"]:
                                state["messages"].append(msg)
                    
                    # Update other fields
                    for k, v in value.items():
                        if k != "messages" and v is not None:
                            state[k] = v
            
            print("[DEBUG] Graph execution completed")
            return state
            
        except Exception as e:
            print(f"[ERROR] Error during graph execution: {str(e)}")
            import traceback
            traceback.print_exc()
            return state
        
        return state
    except Exception as e:
        print(f"Error processing update: {str(e)}")
        state["error"] = str(e)
        return state

async def main():
    print("Chat Agent Test Interface")
    print("Type 'quit', 'exit', or 'q' to end the session")
    print("=" * 80 + "\n")
    
    state = None
    
    while True:
        try:
            user_input = input("\nYou: ").strip()
            if not user_input:
                continue
                
            if user_input.lower() in ["quit", "exit", "q"]:
                print("\nGoodbye!")
                break
            
            # Process the input and get the updated state
            state = await stream_graph_updates(user_input, state)
            
        except KeyboardInterrupt:
            print("\n\nSession interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"\nAn error occurred: {str(e)}")
            if state:
                state["error"] = str(e)

if __name__ == "__main__":
    asyncio.run(main())
