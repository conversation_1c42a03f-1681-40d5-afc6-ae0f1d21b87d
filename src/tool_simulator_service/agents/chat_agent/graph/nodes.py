import os
import json
from typing import Annotated, Literal

from langchain_core.prompts import PromptTemplate
from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool
from langchain_core.llms import ChatOpenAI
from langgraph.types import Command, interrupt
from yaml import safe_load

from src.tool_simulator_service.agents.chat_agent.agent_factory.agent import create_agent, get_llm_by_type
from src.tool_simulator_service.common.logging_setup import get_logger
from src.tool_simulator_service.agents.chat_agent.config.prompts import apply_prompt_template, get_prompt_templates
from src.tool_simulator_service.agents.chat_agent.config.planner_module import Plan
from src.tool_simulator_service.agents.chat_agent.graph.state import State
from src.tool_simulator_service.agents.chat_agent.config.tools import get_incident_report
logger = get_logger(__name__)



def get_incident_node(incident_id : str):#next node = planner node 
    return get_incident_report(incident_id)

def planner_node() -> Command (Literal["humman_feedback_node", "team_router_node"]):#next node = reserch resolve  node
    llm = get_llm_by_type("gpt-4o-2024-08-06")
    prompt_template = get_prompt_templates("planner_node")
    prompt = PromptTemplate.from_template(prompt_template)
    planner_agent = create_agent("planner_agent", "gpt-4o-2024-08-06", [], prompt)
    while State.step != 0:
        
        planner_agent.invoke({"input": "plan the incident", "chat_history": ""}).with_structured_output_parser(Plan)
        if planner_agent.output.has_enough_context:
            return "human_feedback_node"
        else:
            planner_agent.invoke({"input": "plan the incident", "chat_history": ""}).with_structured_output_parser(Plan)
            

def reserch_resolve_node(): #next node = solution agent reporter node or human feedback node
    pass
def solution_agent_node(): #next node = human feedback node
    pass

def human_feedback_node(): #next node = reporter node
    pass

def reporter_node(): #next node = end
    pass

