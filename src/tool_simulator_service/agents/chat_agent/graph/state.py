"""State management for the Incident Analyzer/Resolver Agent.

Defines the ChatAgentState class, which holds all workflow data as the agent
moves through the orchestration graph. Each node reads/updates this state.
"""

from typing import Optional, List, Dict, Any
from langgraph.graph import MessagesState
from src.tool_simulator_service.agents.chat_agent.config.planner_module import Plan

class ChatAgentState(MessagesState):
    """Checkpoint state for incident root cause analysis and resolution workflow.

    Inherits:
        MessagesState: Provides chat history and LLM context.
    """

    # User and incident context
    user_query: str = ""
    """The user's original query or incident report."""

    incident_context: Optional[Dict[str, Any]] = None
    """Context, logs, or metadata for the incident, retrieved by tools."""

    # Planning
    current_plan: Plan | str = None
    """Current plan object (analysis or resolution steps)."""

    plan_iterations: int = 0
    """Number of iterations to generate or revise the plan."""

    # Root Cause Analysis (RCA)
    root_cause: str = ""
    """The summary or findings of the root cause analysis."""

    root_cause_feedback: str = ""
    """User feedback or confirmation about the root cause."""

    rca_tools_used: List[str] = []
    """List of RCA tool names used (e.g., log analyzer, RAG/manuals)."""

    # Resolution
    resolution_plan: str = ""
    """Plan for resolution or mitigation, generated by the agent."""

    resolution_feedback: str = ""
    """User feedback or confirmation about the resolution plan."""

    resolution_steps: Optional[List[Dict[str, Any]]] = None
    """Detailed, structured resolution steps if applicable."""

    # Reporting and intermediate results
    final_report: str = ""
    """Final structured incident report for the user."""

    observations: List[str] = []
    """Intermediate notes, findings, or tool/agent outputs."""

    # Human feedback state
    rc_feedback_status: Optional[str] = None
    """User's feedback status on root cause ('[EDIT_RC]', '[ACCEPTED_RC]')."""

    res_feedback_status: Optional[str] = None
    """User's feedback status on resolution ('[EDIT_RES_PLAN]', '[ACCEPTED_RES_PLAN]')."""

    # Runtime / workflow tracking
    step: str = ""
    """Current workflow node or step name (optional, for debugging/logging)."""

    auto_accepted_plan: bool = False
    """If true, plan was accepted automatically (bypassing human feedback)."""


