# Chat Agent
The chat agent is responsible for managing the conversation between the user and the system. It uses a state machine to manage the conversation and a set of tools to interact with the system. The chat agent is built using the LangChain library.

## Tools
The chat agent uses a set of tools to interact with the system. The tools are defined in the `tools` directory. Each tool is defined in its own file and implements the `BaseTool` interface. The tools are:
- `get_relevant_logs_for_event`: Retrieves relevant logs for a given event
- `get_incident_report`: Retrieves an incident report
- `get_kpi_metrics`: Retrieves KPI metrics