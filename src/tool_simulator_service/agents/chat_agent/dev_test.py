import asyncio
from datetime import datetime
from src.tool_simulator_service.agents.chat_agent.graph.builder import graph
from src.tool_simulator_service.agents.chat_agent.graph.state import ChatAgentState
from langchain_core.messages import HumanMessage, AIMessage

async def run():
    print("\nChat Agent Test Interface")
    print("Type 'quit', 'exit', or 'q' to end the session")
    print("=" * 80)

    while True:
        user_input = input("\nYou: ").strip()
        if user_input.lower() in {"quit", "exit", "q"}:
            print("Exiting...")
            break

        # Prepare initial state
        state = ChatAgentState(
            user_query=user_input,
            messages=[HumanMessage(content=user_input)],
            step="coordinator",                    # Needed for prompts
            auto_accepted_plan=False,              # Must match prompt expectations
            plan_iterations=0,
            plan_approved=False,
            incident_context={"environment": "dev", "service": "test", "severity": "medium"}
        )

        try:
            final_state = await graph.ainvoke(state)
        except Exception as e:
            print(f"\n[ERROR] Agent execution failed:\n{e}")
            continue

        print("\n=== FINAL REPORT ===")
        print(final_state.final_report or "(No report generated)")

        print("\n=== CHAT HISTORY ===")
        for msg in final_state.messages:
            role = msg.type if hasattr(msg, "type") else "unknown"
            print(f"{role.upper()}: {msg.content}")

if __name__ == "__main__":
    asyncio.run(run())
