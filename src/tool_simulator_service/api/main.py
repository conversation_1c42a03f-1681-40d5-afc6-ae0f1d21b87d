"""
Main FastAPI application for the Tool Simulator Service (WebSocket-ready, Kafka-free or Kafka-enabled).
"""

import asyncio
import time
from datetime import datetime
from typing import Any, Dict

from fastapi import FastAPI, Request, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, Response
from prometheus_client import generate_latest, CONTENT_TYPE_LATEST

from ..common.constants import SERVICE_NAME, VERSION, SENSOR_GROUPS
from ..common.exceptions import ToolSimulatorError
from ..common.helpers import get_unix_timestamp
from ..common.logging_setup import get_logger, setup_logging
from ..config.settings import settings, validate_config
from ..api.endpoints import simulation, tools, debug_tools
from ..telemetry.generator import generator
from ..telemetry.streamer import streamer
from ..telemetry.websocket_manager import WebSocketManager
from ..api.schemas import HealthCheckResponse

# Initialize logger
logger = get_logger(__name__)
setup_logging(level="INFO")

# Validate configuration
try:
    validate_config(settings)
except Exception as e:
    logger.critical(f"❌ Config validation failed: {e}")
    raise

# FastAPI app setup
app = FastAPI(
    title=SERVICE_NAME,
    description="Telemetry and tooling backend for glass manufacturing plants.",
    version=VERSION,
    docs_url="/docs",
    redoc_url="/redoc",
)

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development; tighten in prod
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Register routers
app.include_router(simulation.router, prefix="/api/v1/simulation", tags=["Simulation"])
app.include_router(tools.router, prefix="/api/v1/tools", tags=["Tools"])
app.include_router(debug_tools.router, prefix="/api/v1/debug", tags=["Debug Tools"])

# Log plant config
logger.info(f"🚀 Starting Tool Simulator Service for plant: {settings.streaming.plant.name}")
for sensor_type in SENSOR_GROUPS:
    sensor_group = getattr(settings.simulation, f"{sensor_type}_sensors", {})
    zone_count = sum(len(s.get("zones", {})) for s in sensor_group.values())
    logger.info(f"  - {sensor_type.title()} sensors: {len(sensor_group)} types across {zone_count} zones")

# Start time
start_time = get_unix_timestamp()
websocket_manager = streamer.websocket_manager

@app.on_event("startup")
async def on_startup():
    logger.info("🔧 Starting background services...")
    if settings.simulation.enabled:
        await generator.start()
        logger.info("✅ Telemetry generator started")
    if settings.streaming.enabled:
        await streamer.start()


@app.on_event("shutdown")
async def on_shutdown():
    logger.info("🛑 Shutting down services...")
    await generator.stop()
    await streamer.stop()


@app.get("/", response_model=Dict[str, Any])
async def root():
    return {
        "service": SERVICE_NAME,
        "version": VERSION,
        "description": "Streaming telemetry + tools backend",
        "docs_url": "/docs",
        "redoc_url": "/redoc",
        "health_check_url": "/health",
    }


@app.get("/health", response_model=HealthCheckResponse)
async def health_check():
    uptime_seconds = get_unix_timestamp() - start_time
    return {
        "status": "healthy",
        "version": VERSION,
        "uptime_seconds": uptime_seconds,
        "timestamp": datetime.utcnow().isoformat(),
    }


@app.get("/metrics")
async def metrics():
    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)


@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    logger.info(f"📥 {request.method} {request.url.path}")
    response = await call_next(request)
    duration = (time.time() - start_time) * 1000
    logger.info(f"📤 {request.method} {request.url.path} - {response.status_code} - {duration:.2f}ms")
    return response


# WebSocket endpoint: all sensor types
@app.websocket("/ws/telemetry/all")
async def websocket_all(websocket: WebSocket):
    await websocket_manager.connect(websocket, "all")
    try:
        while True:
            await websocket.receive_text()  # Keep connection alive
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket, "all")


# WebSocket endpoints: one per sensor type
for sensor_type in SENSOR_GROUPS:
    path = f"/ws/telemetry/{sensor_type}"

    @app.websocket(path)
    async def websocket_sensor(websocket: WebSocket, sensor_type=sensor_type):
        await websocket_manager.connect(websocket, sensor_type)
        try:
            while True:
                await websocket.receive_text()
        except WebSocketDisconnect:
            websocket_manager.disconnect(websocket, sensor_type)

# WebSocket endpoint: KPI metrics + events
@app.websocket("/ws/telemetry/kpimetrics")
async def websocket_kpi(websocket: WebSocket):
    await websocket_manager.connect(websocket, "kpimetrics")
    try:
        while True:
            await websocket.receive_text()  # Keep connection alive
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket, "kpimetrics")



@app.exception_handler(ToolSimulatorError)
async def handle_tool_sim_error(request: Request, exc: ToolSimulatorError):
    logger.error(f"ToolSimulatorError: {exc}")
    return JSONResponse(
        status_code=500,
        content={"error": "Internal Server Error", "detail": str(exc)},
    )


@app.exception_handler(Exception)
async def handle_generic_error(request: Request, exc: Exception):
    logger.exception(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={"error": "Internal Server Error", "detail": "An unexpected error occurred."},
    )
