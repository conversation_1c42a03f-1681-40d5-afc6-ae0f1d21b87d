# In src/tool_simulator_service/api/endpoints/debug_tools.py
from fastapi import APIRouter, HTTPException, status, Body
from pydantic import BaseModel, Field
from typing import List, Optional # Keep Optional

from ...common.logging_setup import get_logger
from ...telemetry.generator import generator
from ...common.exceptions import NotFoundError, ValidationError, SimulationError, TypeError

logger = get_logger(__name__)
router = APIRouter(
    prefix="/debug",
    tags=["Debug Tools"]
)

# --- MODIFIED Pydantic model for the API request payload ---
class TargetedSpikeInstructionPayload(BaseModel):
    metric_type: str = Field(..., description="The type of metric to spike (e.g., 'temperature', 'hvac').", example="temperature")
    spike_value: float = Field(..., description="The absolute value to set for the metric during the spike.", example=75.0)
    duration_seconds: float = Field(5.0, gt=0, description="How long the spiked value should persist (in seconds).", example=5.0)
    sensor_target_count: int = Field(1, ge=1, description="The number of sensors of this metric_type to spike across all zones (randomly selected).", example=2)

# --- Pydantic model for the API response (can remain the same) ---
class SpikeResponse(BaseModel):
    message: str
    forced_simulators: List[str]


@router.post(
    "/spike-metric-by-type", # Changed endpoint path for clarity
    summary="Temporarily spike a specified number of sensors of a given metric type",
    status_code=status.HTTP_202_ACCEPTED,
    response_model=SpikeResponse
)
async def spike_metric_by_type_endpoint(payload: TargetedSpikeInstructionPayload = Body(...)):
    """
    ## Spike Metric by Type (Targeted Count)

    This endpoint allows you to temporarily force the output of a specified number (`sensor_target_count`)
    of randomly selected simulators matching a specific `metric_type` across **all configured zones**.
    The selected simulators will output the specified `spike_value` for the `duration_seconds`.

    - **`metric_type`**: Corresponds to the `sensor_type` used when creating `ZoneSensor` instances (e.g., 'temperature', 'humidity', 'hvac').
    - **`spike_value`**: The target value for the spike. This value will be used directly, potentially overriding normal sensor limits.
    - **`duration_seconds`**: How long the spike lasts.
    - **`sensor_target_count`**: The number of sensors of this `metric_type` to find and spike. If more sensors exist than the count, a random selection is made. If fewer exist, all matching will be spiked.

    This is useful for testing the system's response to anomalies affecting a subset of sensors of a particular type.
    """
    logger.info(f"Received API request to spike metric by type: {payload.dict()}")
    try:
        # Call a new/modified method in the generator instance
        result = await generator.spike_random_sensors_by_metric_type( # New method name
            metric_type=payload.metric_type,
            spike_value=payload.spike_value,
            duration_seconds=payload.duration_seconds,
            target_count=payload.sensor_target_count
        )
        logger.info(f"Spike metric by type request processed: {result.get('message')}")
        return SpikeResponse(**result)

    except NotFoundError as e:
        logger.warning(f"Failed to spike metric by type - Not Found: {e}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except (TypeError, ValueError, ValidationError, SimulationError) as e:
         logger.error(f"Failed to spike metric by type - Bad Request/Error: {e}")
         raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.exception(
            f"Unexpected error spiking metric by type '{payload.metric_type}'"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected server error occurred."
        )