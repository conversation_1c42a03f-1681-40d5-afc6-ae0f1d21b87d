"""
API endpoints for tool execution.
"""
import asyncio
import json
import time
from typing import Any, Dict, List, Optional

from fastapi import APIRout<PERSON>, Depends, HTTPException, Path, Query, Body
from fastapi.responses import JSONResponse
from fastapi.concurrency import run_in_threadpool
from ...common.exceptions import ToolError, ValidationError
from ...common.helpers import get_timestamp
from ...common.logging_setup import get_logger
from ...telemetry.generator import generator
from ...tools.registry import tool_registry
from ...api.schemas import (
    ErrorResponse,
    ToolExecutionRequest,
    ToolExecutionResponse,
    ToolInfoResponse,
    ToolsListResponse,
    IncidentReportModel,
    IncidentAnalysisRequest,
)
from src.tool_simulator_service.agents.incident_agent.agent import run_incident_analysis

logger = get_logger(__name__)

router = APIRouter()


@router.get("/", response_model=ToolsListResponse)
async def list_tools():
    """
    List all available tools.
    """
    try:
        tools = tool_registry.list_tools()
        tool_info = []
        
        for tool_id in tools:
            tool = tool_registry.get_tool(tool_id)
            if tool:
                tool_info.append({
                    "tool_id": tool_id,
                    "name": tool.name,
                    "description": tool.description,
                    "input_schema": tool.get_input_schema(),
                    "output_schema": tool.get_output_schema(),
                    "options_schema": tool.get_options_schema(),
                })
        
        return {
            "tools": tool_info,
            "total": len(tool_info),
        }
    except Exception as e:
        logger.exception(f"Error listing tools: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error listing tools: {str(e)}",
        )


@router.get("/{tool_id}", response_model=ToolInfoResponse)
async def get_tool_info(tool_id: str = Path(..., description="ID of the tool")):
    """
    Get information about a specific tool.
    """
    try:
        tool = tool_registry.get_tool(tool_id)
        if not tool:
            raise HTTPException(
                status_code=404,
                detail=f"Tool not found: {tool_id}",
            )
        
        return {
            "tool_id": tool_id,
            "name": tool.name,
            "description": tool.description,
            "input_schema": tool.get_input_schema(),
            "output_schema": tool.get_output_schema(),
            "options_schema": tool.get_options_schema(),
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error getting tool info: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting tool info: {str(e)}",
        )


@router.post("/{tool_id}/execute", response_model=ToolExecutionResponse)
async def execute_tool(
    request: ToolExecutionRequest,
    tool_id: str = Path(..., description="ID of the tool to execute"),
):
    """
    Execute a specific tool with the provided inputs.
    """
    try:
        # Get the tool
        tool = tool_registry.get_tool(tool_id)
        if not tool:
            raise HTTPException(
                status_code=404,
                detail=f"Tool not found: {tool_id}",
            )
        
        # Validate inputs
        try:
            tool.validate_inputs(request.inputs)
        except ValidationError as e:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid inputs: {str(e)}",
            )
        
        # Execute the tool
        start_time = time.time()
        try:
            result = await tool.execute(request.inputs, request.options)
            status = "success"
            error = None
        except ToolError as e:
            logger.error(f"Tool execution error: {e}")
            result = None
            status = "error"
            error = str(e)
        except Exception as e:
            logger.exception(f"Unexpected error executing tool: {e}")
            result = None
            status = "error"
            error = f"Unexpected error: {str(e)}"
        
        execution_time_ms = (time.time() - start_time) * 1000
        
        # Return the result
        return {
            "tool_id": tool_id,
            "status": status,
            "result": result,
            "error": error,
            "execution_time_ms": execution_time_ms,
            "timestamp": get_timestamp(),
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error executing tool: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error executing tool: {str(e)}",
        )


@router.post("/analyze_incident_event", response_model=IncidentReportModel)
async def analyze_incident_from_event_data(
    request_body: IncidentAnalysisRequest = Body(
{
    "type": "event",
    "event": "machine_uptime_change",
    "metric": "Machine_Uptime",
    "value": 75.0,
    "previous": 91.67,
    "delta_pct": -18.18,
    "timestamp": "2025-05-21T10:47:41.958246"
}
    )
):
    """
    Receives a KPI event or incident data,
    analyzes surrounding telemetry data, and generates an incident report.
    
    The function expects a request body matching the IncidentAnalysisRequest schema,
    which includes fields like type, event, metric, value, previous, delta_pct, and timestamp.
    This matches the format used in testing with run_incident_analysis.
    """
    try:
        # Convert the request body to a JSON string to pass to the agent
        # This matches how the test directly passes a JSON string to run_incident_analysis
        incident_event_json = json.dumps(request_body.dict())
        
        # Pass the JSON string to the run_incident_analysis function
        incident_report_model: IncidentReportModel = await run_in_threadpool(run_incident_analysis, incident_event_json)
        
        return incident_report_model
    except HTTPException:
        raise
    except RuntimeError as e:
        logger.error(f"Agent failed to generate incident report: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.exception(f"Error during incident analysis: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"An unexpected error occurred during incident analysis: {str(e)}",
        )# Function to get telemetry data for tools
    
async def get_telemetry_data(
    sensor_types: Optional[List[str]] = None,
    simulator_ids: Optional[List[str]] = None,
    limit: int = 100,
):
    """
    Get recent telemetry data for tools to use.
    
    Args:
        sensor_types: Optional list of sensor types to filter by
        simulator_ids: Optional list of simulator IDs to filter by
        limit: Maximum number of data points to return
        
    Returns:
        List[Dict[str, Any]]: List of telemetry data points
    """
    # This is a placeholder. In a real implementation, you would:
    # 1. Get data from a buffer or database
    # 2. Filter by sensor types and simulator IDs
    # 3. Limit the number of results
    # 4. Return the data
    
    # For now, just return an empty list
    return []
