"""
API endpoints for tool execution.
"""
import asyncio
import json
import time
from typing import Any, Dict, List, Optional

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, Path, Query, Body
from fastapi.responses import JSONResponse, StreamingResponse
from fastapi.concurrency import run_in_threadpool
from sse_starlette.sse import ServerSentEvent, EventSourceResponse
from ...common.exceptions import ToolError, ValidationError
from ...common.helpers import get_timestamp
from ...common.logging_setup import get_logger
from ...telemetry.generator import generator
from ...tools.registry import tool_registry
from ...api.schemas import (
    ErrorResponse,
    ToolExecutionRequest,
    ToolExecutionResponse,
    ToolInfoResponse,
    ToolsListResponse,
    IncidentReportModel,
    IncidentAnalysisRequest,
    ChatAgentRequest
)
from src.tool_simulator_service.agents.incident_agent.agent import run_incident_analysis
from src.tool_simulator_service.agents.chat_agent.chat_agent_demo import run_chat_agent
from fastapi.responses import StreamingResponse
from tool_simulator_service.agents.chat_agent.callbacks import ThreadedCallbackHandler


logger = get_logger(__name__)

router = APIRouter()


@router.get("/", response_model=ToolsListResponse)
async def list_tools():
    """
    List all available tools.
    """
    try:
        tools = tool_registry.list_tools()
        tool_info = []
        
        for tool_id in tools:
            tool = tool_registry.get_tool(tool_id)
            if tool:
                tool_info.append({
                    "tool_id": tool_id,
                    "name": tool.name,
                    "description": tool.description,
                    "input_schema": tool.get_input_schema(),
                    "output_schema": tool.get_output_schema(),
                    "options_schema": tool.get_options_schema(),
                })
        
        return {
            "tools": tool_info,
            "total": len(tool_info),
        }
    except Exception as e:
        logger.exception(f"Error listing tools: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error listing tools: {str(e)}",
        )


@router.get("/{tool_id}", response_model=ToolInfoResponse)
async def get_tool_info(tool_id: str = Path(..., description="ID of the tool")):
    """
    Get information about a specific tool.
    """
    try:
        tool = tool_registry.get_tool(tool_id)
        if not tool:
            raise HTTPException(
                status_code=404,
                detail=f"Tool not found: {tool_id}",
            )
        
        return {
            "tool_id": tool_id,
            "name": tool.name,
            "description": tool.description,
            "input_schema": tool.get_input_schema(),
            "output_schema": tool.get_output_schema(),
            "options_schema": tool.get_options_schema(),
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error getting tool info: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting tool info: {str(e)}",
        )


@router.post("/{tool_id}/execute", response_model=ToolExecutionResponse)
async def execute_tool(
    request: ToolExecutionRequest,
    tool_id: str = Path(..., description="ID of the tool to execute"),
):
    """
    Execute a specific tool with the provided inputs.
    """
    try:
        # Get the tool
        tool = tool_registry.get_tool(tool_id)
        if not tool:
            raise HTTPException(
                status_code=404,
                detail=f"Tool not found: {tool_id}",
            )
        
        # Validate inputs
        try:
            tool.validate_inputs(request.inputs)
        except ValidationError as e:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid inputs: {str(e)}",
            )
        
        # Execute the tool
        start_time = time.time()
        try:
            result = await tool.execute(request.inputs, request.options)
            status = "success"
            error = None
        except ToolError as e:
            logger.error(f"Tool execution error: {e}")
            result = None
            status = "error"
            error = str(e)
        except Exception as e:
            logger.exception(f"Unexpected error executing tool: {e}")
            result = None
            status = "error"
            error = f"Unexpected error: {str(e)}"
        
        execution_time_ms = (time.time() - start_time) * 1000
        
        # Return the result
        return {
            "tool_id": tool_id,
            "status": status,
            "result": result,
            "error": error,
            "execution_time_ms": execution_time_ms,
            "timestamp": get_timestamp(),
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error executing tool: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error executing tool: {str(e)}",
        )


@router.post("/analyze_incident_event", response_model=IncidentReportModel)
async def analyze_incident_from_event_data(
    request_body: IncidentAnalysisRequest = Body(
{
    "type": "event",
    "event": "machine_uptime_change",
    "metric": "Machine_Uptime",
    "value": 75.0,
    "previous": 91.67,
    "delta_pct": -18.18,
    "timestamp": "2025-05-21T10:47:41.958246"
}
    )
):
    """
    Receives a KPI event or incident data,
    analyzes surrounding telemetry data, and generates an incident report.
    
    The function expects a request body matching the IncidentAnalysisRequest schema,
    which includes fields like type, event, metric, value, previous, delta_pct, and timestamp.
    This matches the format used in testing with run_incident_analysis.
    """
    try:
        # Convert the request body to a JSON string to pass to the agent
        # This matches how the test directly passes a JSON string to run_incident_analysis
        incident_event_json = json.dumps(request_body.dict())
        
        # Pass the JSON string to the run_incident_analysis function
        incident_report_model: IncidentReportModel = await run_in_threadpool(run_incident_analysis, incident_event_json)
        
        return incident_report_model
    except HTTPException:
        raise
    except RuntimeError as e:
        logger.error(f"Agent failed to generate incident report: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.exception(f"Error during incident analysis: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"An unexpected error occurred during incident analysis: {str(e)}",
        )# Function to get telemetry data for tools
    
async def get_telemetry_data(
    sensor_types: Optional[List[str]] = None,
    simulator_ids: Optional[List[str]] = None,
    limit: int = 100,
):
    """
    Get recent telemetry data for tools to use.
    
    Args:
        sensor_types: Optional list of sensor types to filter by
        simulator_ids: Optional list of simulator IDs to filter by
        limit: Maximum number of data points to return
        
    Returns:
        List[Dict[str, Any]]: List of telemetry data points
    """
    # This is a placeholder. In a real implementation, you would:
    # 1. Get data from a buffer or database
    # 2. Filter by sensor types and simulator IDs
    # 3. Limit the number of results
    # 4. Return the data
    
    # For now, just return an empty list
    return []


def agent_task_wrapper(
    queue: asyncio.Queue,
    loop: asyncio.AbstractEventLoop,
    request_data: ChatAgentRequest
):
    """
    This function runs in the background thread.
    It is now responsible for the FULL lifecycle: running the agent,
    queuing the final answer, and queuing the final 'None' signal.
    """
    handler = ThreadedCallbackHandler(queue, loop)
    try:
        final_result = run_chat_agent(
            user_query=request_data.user_query,
            incident_context=request_data.incident_context,
            output_mode=request_data.output_mode,
            callbacks=[handler]
        )
        
        final_answer = final_result.get('output', 'Error: Could not retrieve final answer.')
        final_answer_event = ServerSentEvent(
            data=json.dumps({"type": "final_answer", "content": final_answer}),
            event="final_answer"
        )
        loop.call_soon_threadsafe(queue.put_nowait, final_answer_event)

    except Exception as e:
        error_message = json.dumps({"type": "error", "content": f"An error occurred in the agent task: {e}"})
        error_event = ServerSentEvent(data=error_message, event="error")
        loop.call_soon_threadsafe(queue.put_nowait, error_event)
    finally:
        # ---- THIS IS THE CRUCIAL CHANGE ----
        # After everything is done (successfully or with an error),
        # we send the 'None' signal to close the stream.
        loop.call_soon_threadsafe(queue.put_nowait, None)
    
    
@router.post("/stream-chat-agent")
async def stream_chat_agent_endpoint(
    request_data: ChatAgentRequest = Body(
        ...,
        example={
            "user_query": "Tell me about this incident and suggest a resolution",
            "incident_context": {
                "timestamp": "2025-06-18T16:11:41+0530",
                "level": "INFO",
                "logger_name": "incident_logger",
                "id": "a547e716-02f4-434f-92e6-9e2e3cb288a0",
                "display_id": "INC-002",
                "title": "Significant OEE Drop Due to Temperature Anomalies and Machine Uptime Reduction",
                "status": "in progress",
                "severity": "high",
                "assigned_team": "Maintenance Team",
                "reported_at": "2025-06-18T10:41:27Z",
                "description": "An OEE drop from 88.06 to 72.11 was observed, coinciding with a series of temperature anomalies in temperature_zone_temperature_zoneB. These anomalies showed a significant increase in temperature, potentially affecting machine performance. Additionally, a machine uptime reduction from 91.67 to 75.0 was recorded at the same time, indicating a possible correlation between the temperature anomalies and machine performance issues.",
                "updates": [
                    "Log analysis initiated based on pivot event.",
                    "Relevant logs from window 2025-06-18T10:41:11 to 2025-06-18T10:41:27 fetched."
                ],
                "event_occurrence_timestamp": "2025-06-18T10:41:41.696423+00:00"
            },
            "output_mode": "full"
        },
        openapi_examples={
            "full_analysis": {
                "summary": "Full analysis of incident",
                "description": "Get complete analysis including root cause and resolution",
                "value": {
                    "user_query": "Tell me about this incident and suggest a resolution",
                    "incident_context": {
                        "timestamp": "2025-06-18T16:11:41+0530",
                        "level": "INFO",
                        "logger_name": "incident_logger",
                        "id": "a547e716-02f4-434f-92e6-9e2e3cb288a0",
                        "display_id": "INC-002",
                        "title": "Significant OEE Drop Due to Temperature Anomalies",
                        "status": "in progress",
                        "severity": "high",
                        "assigned_team": "Maintenance Team",
                        "reported_at": "2025-06-18T10:41:27Z",
                        "description": "OEE drop from 88.06 to 72.11 with temperature anomalies in zone B",
                        "updates": ["Initial analysis started"],
                        "event_occurrence_timestamp": "2025-06-18T10:41:41.696423+00:00"
                    },
                    "output_mode": "full"
                }
            },
            "root_cause_only": {
                "summary": "Root cause analysis only",
                "description": "Get only the root cause of the incident",
                "value": {
                    "user_query": "What caused this incident?",
                    "incident_context": {
                        "display_id": "INC-002",
                        "title": "OEE Drop in Zone B",
                        "description": "Performance drop in manufacturing line B"
                    },
                    "output_mode": "root_cause"
                }
            },
            "resolution_only": {
                "summary": "Resolution only",
                "description": "Get only the recommended resolution",
                "value": {
                    "user_query": "How can we fix this?",
                    "incident_context": {
                        "display_id": "INC-002",
                        "title": "OEE Drop in Zone B",
                        "description": "Performance drop in manufacturing line B"
                    },
                    "output_mode": "resolution"
                }
            }
        }
    )
):
    """
    Analyzes an incident and streams the agent's status updates,
    followed by the final answer.
    
    The endpoint accepts a ChatAgentRequest with the following parameters:
    - user_query: The question or request from the user
    - incident_context: Detailed information about the incident
    - output_mode: Determines the type of response:
        - 'full': Complete analysis (root cause + resolution)
        - 'root_cause': Only the root cause analysis
        - 'resolution': Only the recommended resolution
    
    The response is streamed as Server-Sent Events (SSE) with the following event types:
    - status: Updates about the agent's thought process
    - final_answer: The complete response from the agent
    """
    loop = asyncio.get_running_loop()
    queue = asyncio.Queue()
    handler = ThreadedCallbackHandler(queue, loop)

    # The background task will run the synchronous agent function
    # in a separate thread to avoid blocking the server.
    loop.run_in_executor(
        None,  # Use the default thread pool executor
        agent_task_wrapper, # The wrapper function that queues ALL events
        queue,
        loop,
        request_data
    )

    async def event_stream_generator():
        while True:
            event = await queue.get()
            if event is None:
                # The on_chain_end callback sends None to signal the end of everything
                break
            yield event

    return EventSourceResponse(event_stream_generator())