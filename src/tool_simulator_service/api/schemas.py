"""
API schemas for the Tool Simulator Service.
"""
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union, Literal

from pydantic import BaseModel, Field

from ..common.constants import (
    SIM_STATE_STOPPED,
    SIM_STATE_RUNNING,
    SIM_STATE_PAUSED,
    SIM_STATE_ERROR,
)


class SimulationAction(str, Enum):
    """Enum for simulation control actions."""
    START = "start"
    STOP = "stop"
    PAUSE = "pause"
    RESET = "reset"


class SimulationControlRequest(BaseModel):
    """Request model for simulation control."""
    action: SimulationAction = Field(..., description="Action to perform on the simulation")
    simulators: Optional[List[str]] = Field(
        None, description="List of simulator IDs to control, or None for all"
    )


class SimulationStatusResponse(BaseModel):
    """Response model for simulation status."""
    status: str = Field(..., description="Current simulation status")
    running: bool = Field(..., description="Whether the simulation is running")
    start_time: Optional[str] = Field(None, description="Simulation start time (ISO format)")
    uptime_seconds: Optional[float] = Field(None, description="Simulation uptime in seconds")
    active_simulators: List[str] = Field([], description="List of active simulator IDs")
    data_points_generated: int = Field(0, description="Number of data points generated")
    messages_sent: int = Field(0, description="Number of messages sent")


class SimulatorInfoResponse(BaseModel):
    simulator_id: str = Field(..., description="Unique identifier for the simulator")
    name: str = Field(..., description="Human-readable name for the simulator")
    type: str = Field(..., description="Type of simulator")
    sensor_type: Optional[str] = Field(default=None, description="Type of sensor (if applicable)")
    config: Dict[str, Any] = Field(default_factory=dict, description="Simulator configuration")
    is_running: bool = Field(default=False, description="Whether the simulator is running")
    data_points_generated: int = Field(default=0, description="Number of data points generated")
    last_value: Optional[Any] = Field(default=None, description="Last generated value")
    last_update_time: Optional[str] = Field(default=None, description="Last update time (ISO format)")



class SimulatorsListResponse(BaseModel):
    """Response model for list of simulators."""
    simulators: List[SimulatorInfoResponse] = Field(..., description="List of simulators")
    total: int = Field(..., description="Total number of simulators")
    running: int = Field(..., description="Number of running simulators")


class StreamingStatusResponse(BaseModel):
    """Response model for streaming status."""
    streaming: Dict[str, Any] = Field(..., description="General streaming status")
    websocket: Dict[str, Any] = Field(..., description="WebSocket status")
    kafka: Dict[str, Any] = Field(..., description="Kafka status")


class ToolExecutionRequest(BaseModel):
    """Request model for tool execution."""
    inputs: Dict[str, Any] = Field(..., description="Tool input parameters")
    options: Optional[Dict[str, Any]] = Field(None, description="Tool execution options")


class ToolExecutionResponse(BaseModel):
    """Response model for tool execution."""
    tool_id: str = Field(..., description="ID of the executed tool")
    status: str = Field(..., description="Execution status")
    result: Optional[Any] = Field(None, description="Tool execution result")
    error: Optional[str] = Field(None, description="Error message if execution failed")
    execution_time_ms: float = Field(..., description="Execution time in milliseconds")
    timestamp: str = Field(..., description="Execution timestamp (ISO format)")


class ToolInfoResponse(BaseModel):
    """Response model for tool information."""
    tool_id: str = Field(..., description="Unique identifier for the tool")
    name: str = Field(..., description="Human-readable name for the tool")
    description: str = Field(..., description="Tool description")
    input_schema: Dict[str, Any] = Field(..., description="JSON Schema for tool inputs")
    output_schema: Optional[Dict[str, Any]] = Field(None, description="JSON Schema for tool outputs")
    options_schema: Optional[Dict[str, Any]] = Field(None, description="JSON Schema for tool options")


class ToolsListResponse(BaseModel):
    """Response model for list of tools."""
    tools: List[ToolInfoResponse] = Field(..., description="List of available tools")
    total: int = Field(..., description="Total number of tools")

class SimulatorFiltersResponse(BaseModel):
    sensor_types: List[str]
    zone_ids: List[str]


class ErrorResponse(BaseModel):
    """Response model for errors."""
    error: str = Field(..., description="Error message")
    detail: Optional[str] = Field(None, description="Detailed error information")
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat(), description="Error timestamp (ISO format)")


class HealthCheckResponse(BaseModel):
    """Response model for health check."""
    status: str = Field(..., description="Service status")
    version: str = Field(..., description="Service version")
    uptime_seconds: float = Field(..., description="Service uptime in seconds")
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat(), description="Health check timestamp (ISO format)")


class TelemetryDataPoint(BaseModel):
    timestamp: str = Field(..., description="Data point timestamp (ISO format)")
    simulator_id: str = Field(..., description="ID of the simulator that generated the data")
    name: str = Field(..., description="Human-readable name for the data source")
    sensor_type: str = Field(..., description="Type of sensor")
    value: Optional[float] = Field(default=None, description="Sensor value (for continuous sensors)")
    unit: Optional[str] = Field(default=None, description="Unit of measurement (for continuous sensors)")
    state: Optional[str] = Field(default=None, description="State value (for discrete sensors)")
    is_spiking: Optional[bool] = Field(default=None, description="Whether the sensor is spiking (for vibration sensors)")
    is_defect_active: Optional[bool] = Field(default=None, description="Whether a defect is active (for quality sensors)")
    variance: Optional[float] = Field(default=None, description="Value variance (for quality sensors)")
    cycle_phase: Optional[str] = Field(default=None, description="Cycle phase (for speed sensors)")


class TelemetryDataResponse(BaseModel):
    """Response model for telemetry data."""
    plant_id: str = Field(..., description="ID of the plant")
    plant_name: str = Field(..., description="Name of the plant")
    timestamp: str = Field(..., description="Message timestamp (ISO format)")
    data: List[TelemetryDataPoint] = Field(..., description="Telemetry data points")

class SpikeInstruction(BaseModel):
    metric_type: str = Field(..., description="The type of metric to spike (e.g., 'temperature', 'hvac', 'illumination', 'aqi'). Must match a configured sensor type.")
    zone_id: str = Field(..., description="The zone ID to target (e.g., 'zoneA', 'zoneB').")
    spike_value: float = Field(..., description="The absolute value to set for the metric during the spike.")
    duration_seconds: float = Field(5.0, gt=0, description="How long the spiked value should persist (in seconds).")
    sensor_count_to_spike: Optional[int] = Field(1, ge=1, description="Number of sensors of this type in this zone to spike. If null, spikes all matching. If > available, spikes all available.")

class SpikeResponse(BaseModel):
    message: str
    forced_simulators: List[str]

class IncidentAnalysisRequest(BaseModel):
    """Request model for incident analysis based on KPI events."""
    type: str = Field(..., description="Type of event (e.g., 'kpi_event')")
    event: str = Field(..., description="Specific event type (e.g., 'oee_change')")
    metric: str = Field(..., description="Metric name that triggered the event")
    value: float = Field(..., description="Current value of the metric")
    previous: float = Field(..., description="Previous value of the metric")
    delta_pct: float = Field(..., description="Percentage change in the metric")
    timestamp: str = Field(..., description="Timestamp of the event (ISO format)")


class IncidentReportModel(BaseModel):
    """Response model for incident report."""
    id: str
    display_id: str
    title: str
    status: Literal["in progress", "resolved"]
    severity: Literal["low", "medium", "high"]
    assigned_team: str
    reported_at: str
    description: str
    updates: List[str]

class ChatAgentRequest(BaseModel):
    user_query: str
    incident_context: Dict[str, Any]
    output_mode: Literal["full", "root_cause", "resolution"] = "full"