# Setup Guide

## Environment Setup

Create a conda environment using the environment.yaml file:
```bash
conda env create -f environment.yaml
conda activate te
```
setup the env file 
```bash
cp .env.example .env
```
paste your openai key in the .env file

## Running the Service
 export $(cat .env | xargs)

Run the below command to start the service:
```bash
PYTHONPATH=./src uvicorn tool_simulator_service.api.main:app --reload
```

## API Documentation

Access the FastAPI documentation at:
```
http://localhost:8000/docs
```

## WebSocket URLs

Connect to these WebSocket endpoints to receive real-time telemetry data:
```
ws://localhost:8000/ws/telemetry/all
ws://localhost:8000/ws/telemetry/kpimetrics
```