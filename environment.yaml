name: te
channels:
  - defaults
  - https://repo.anaconda.com/pkgs/main
  - https://repo.anaconda.com/pkgs/r
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2025.2.25=h06a4308_0
  - ld_impl_linux-64=2.40=h12ee557_0
  - libffi=3.4.4=h6a678d5_1
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libstdcxx-ng=11.2.0=h1234567_1
  - libuuid=1.41.5=h5eee18b_0
  - ncurses=6.4=h6a678d5_0
  - openssl=1.1.1w=h7f8727e_0
  - pip=25.0=py311h06a4308_0
  - python=3.11.0=h7a1cb2a_3
  - readline=8.2=h5eee18b_0
  - setuptools=75.8.0=py311h06a4308_0
  - sqlite=3.45.3=h5eee18b_0
  - tk=8.6.14=h39e8969_0
  - tzdata=2025a=h04d1e81_0
  - wheel=0.45.1=py311h06a4308_0
  - xz=5.6.4=h5eee18b_1
  - zlib=1.2.13=h5eee18b_1
  - pip:
      - aiokafka==0.12.0
      - annotated-types==0.7.0
      - anyio==4.9.0
      - async-timeout==5.0.1
      - certifi==2025.1.31
      - charset-normalizer==3.4.2
      - click==8.1.8
      - directory-tree==1.0.0
      - distro==1.9.0
      - fastapi==0.115.12
      - greenlet==3.2.2
      - h11==0.14.0
      - httpcore==1.0.7
      - httpx==0.28.1
      - idna==3.10
      - iniconfig==2.1.0
      - jiter==0.9.0
      - jsonpatch==1.33
      - jsonpointer==3.0.0
      - langchain==0.3.25
      - langchain-core==0.3.59
      - langchain-openai==0.3.16
      - langchain-text-splitters==0.3.8
      - langgraph==0.4.3
      - langgraph-checkpoint==2.0.25
      - langgraph-prebuilt==0.1.8
      - langgraph-sdk==0.1.69
      - langsmith==0.3.42
      - loguru==0.7.3
      - markdown-it-py==3.0.0
      - mdurl==0.1.2
      - openai==1.78.1
      - orjson==3.10.18
      - ormsgpack==1.9.1
      - packaging==24.2
      - pluggy==1.5.0
      - prometheus-client==0.21.1
      - pydantic==2.11.3
      - pydantic-core==2.33.1
      - pygments==2.19.1
      - pytest==8.3.5
      - pytest-asyncio==0.26.0
      - python-dotenv==1.1.0
      - pyyaml==6.0.2
      - regex==2024.11.6
      - requests==2.32.3
      - requests-toolbelt==1.0.0
      - rich==14.0.0
      - sniffio==1.3.1
      - sqlalchemy==2.0.40
      - starlette==0.46.1
      - tenacity==9.1.2
      - tiktoken==0.9.0
      - tqdm==4.67.1
      - typing-extensions==4.13.1
      - typing-inspection==0.4.0
      - urllib3==2.4.0
      - uvicorn==0.34.0
      - websockets==15.0.1
      - xxhash==3.5.0
      - zstandard==0.23.0
prefix: /home/<USER>/anaconda3/envs/te
